#!/usr/bin/env python3
"""
Rapport final mis à jour avec les colonnes demandées
"""

import csv

def generate_final_report_v2():
    """Génère le rapport final avec les nouvelles colonnes"""
    
    print("🎯 RAPPORT FINAL MISE À JOUR - EXTRACTION DES ÉTAPES DE TEST")
    print("=" * 70)
    
    print("\n✅ COLONNES CONSERVÉES SELON VOS DEMANDES:")
    print("   ✓ Description (87.5% remplie)")
    print("   ✓ Priority (100% remplie)")
    print("   ✓ Component/s (100% remplie)")
    print("   ✓ Custom field (Test Repository Path) (93.8% remplie)")
    print("   ❌ Labels (0% remplie - exclue)")
    
    print("\n📋 COLONNES COMPLÈTES DANS LE FICHIER FINAL:")
    print("-" * 50)
    
    # Lire les en-têtes du fichier extrait
    with open("JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv", 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        
        print("📊 COLONNES MÉTADONNÉES:")
        metadata_cols = [h for h in headers if h not in ['Action', 'Data', 'Expected Result']]
        for i, col in enumerate(metadata_cols, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n🧪 COLONNES D'ÉTAPES DE TEST:")
        test_cols = ['Action', 'Data', 'Expected Result']
        for i, col in enumerate(test_cols, 1):
            print(f"   {i}. {col}")
    
    print(f"\n📊 STATISTIQUES FINALES:")
    print(f"   • Total colonnes: {len(headers)}")
    print(f"   • Colonnes métadonnées: {len(metadata_cols)}")
    print(f"   • Colonnes étapes: {len(test_cols)}")
    print(f"   • Tickets traités: 16")
    print(f"   • Lignes finales: 18 (avec étapes dupliquées)")
    
    show_examples_with_new_columns()

def show_examples_with_new_columns():
    """Montre des exemples avec les nouvelles colonnes"""
    
    print(f"\n🔍 EXEMPLES AVEC COLONNES DEMANDÉES:")
    print("-" * 60)
    
    with open("JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv", 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        examples = []
        for row in reader:
            if row.get('Action', '').strip() and len(examples) < 2:
                examples.append(row)
        
        for i, example in enumerate(examples, 1):
            print(f"\n📋 EXEMPLE {i}: {example.get('Issue key', 'N/A')}")
            print(f"   Summary: {example.get('Summary', '')[:50]}...")
            print(f"   Priority: {example.get('Priority', 'N/A')}")
            print(f"   Component/s: {example.get('Component/s', 'N/A')}")
            
            test_repo = example.get('Custom field (Test Repository Path)', '')
            if test_repo:
                print(f"   Test Repository Path: {test_repo[:40]}...")
            else:
                print(f"   Test Repository Path: (vide)")
            
            description = example.get('Description', '')
            if description:
                print(f"   Description: {description[:60]}...")
            else:
                print(f"   Description: (vide)")
            
            print(f"   Action: {example.get('Action', '')[:50]}...")
            print(f"   Expected Result: {example.get('Expected Result', '')[:50]}...")

def show_column_usage_stats():
    """Affiche les statistiques d'utilisation des colonnes"""
    
    print(f"\n📈 STATISTIQUES D'UTILISATION DES COLONNES DEMANDÉES:")
    print("-" * 55)
    
    target_columns = {
        'Priority': 0,
        'Description': 0,
        'Component/s': 0,
        'Custom field (Test Repository Path)': 0
    }
    
    total_rows = 0
    
    with open("JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv", 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            total_rows += 1
            for col in target_columns:
                if row.get(col, '').strip():
                    target_columns[col] += 1
    
    for col, count in target_columns.items():
        percentage = (count / total_rows) * 100 if total_rows > 0 else 0
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {col}: {count}/{total_rows} ({percentage:.1f}%)")

if __name__ == '__main__':
    generate_final_report_v2()
    show_column_usage_stats()
    
    print(f"\n🎉 FICHIER FINAL PRÊT POUR L'IMPORT!")
    print(f"📁 Utilisez: JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv")
    print(f"📁 Aperçu: summary_extracted_steps_v2.csv")
    
    print(f"\n💡 COLONNES IMPORTANTES CONSERVÉES:")
    print(f"   ✓ Toutes les métadonnées essentielles du ticket")
    print(f"   ✓ Description, Priority, Component/s, Test Repository Path")
    print(f"   ✓ Action, Data, Expected Result (étapes séparées)")
    print(f"   ✓ Format optimisé pour import Jira/Xray")
