#!/usr/bin/env python3
"""
Script d'exécution pour la migration Jira/Xray
Utilise l'outil principal et génère un rapport de validation
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migration():
    """Exécute la migration complète"""
    
    # Fichier d'entrée (votre export Jira)
    input_file = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
    
    if not os.path.exists(input_file):
        logger.error(f"Fichier d'entrée non trouvé: {input_file}")
        return False
    
    # Générer les noms de fichiers de sortie avec timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"JIRA_Orange_migrated_{timestamp}.csv"
    report_file = f"migration_report_{timestamp}.txt"
    
    logger.info("🚀 Début de la migration Jira/Xray")
    logger.info(f"Fichier d'entrée: {input_file}")
    logger.info(f"Fichier de sortie: {output_file}")
    
    try:
        # Étape 1: Exécuter la migration
        logger.info("📝 Étape 1: Traitement et formatage des données...")
        
        from jira_xray_migration_tool import JiraXrayMigrationTool
        
        tool = JiraXrayMigrationTool(input_file, output_file)
        success = tool.process_csv('html')
        
        if not success:
            logger.error("❌ Échec de la migration")
            return False
        
        logger.info("✅ Migration terminée avec succès")
        
        # Étape 2: Validation
        logger.info("🔍 Étape 2: Validation des résultats...")
        
        from validate_migration import MigrationValidator
        
        validator = MigrationValidator(input_file, output_file)
        report = validator.generate_report()
        
        # Sauvegarder le rapport
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info("📊 Rapport de validation généré")
        print("\n" + report)
        
        # Étape 3: Résumé final
        logger.info("📋 RÉSUMÉ DE LA MIGRATION")
        logger.info(f"✅ Fichier migré: {output_file}")
        logger.info(f"📊 Rapport de validation: {report_file}")
        
        # Vérifier la taille des fichiers
        original_size = os.path.getsize(input_file)
        migrated_size = os.path.getsize(output_file)
        
        logger.info(f"📏 Taille fichier original: {original_size:,} bytes")
        logger.info(f"📏 Taille fichier migré: {migrated_size:,} bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la migration: {e}")
        return False

def main():
    """Point d'entrée principal"""
    print("=" * 60)
    print("OUTIL DE MIGRATION JIRA/XRAY")
    print("Reformatage des tickets de test pour import")
    print("=" * 60)
    print()
    
    success = run_migration()
    
    if success:
        print("\n🎉 Migration terminée avec succès!")
        print("\nPROCHAINES ÉTAPES:")
        print("1. Vérifiez le rapport de validation")
        print("2. Examinez le fichier migré")
        print("3. Testez l'import dans votre nouveau projet Jira")
        print("4. Vérifiez que la mise en forme est préservée")
        return 0
    else:
        print("\n❌ Migration échouée. Consultez les logs pour plus de détails.")
        return 1

if __name__ == '__main__':
    exit(main())
