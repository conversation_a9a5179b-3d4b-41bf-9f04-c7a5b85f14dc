#!/usr/bin/env python3
"""
Extracteur d'étapes de test Jira/Xray
Convertit les étapes JSON en lignes séparées avec colonnes Action, Data, Expected Result
"""

import csv
import json
import logging
from typing import List, Dict, Any
from datetime import datetime
import os

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_test_steps(input_file: str, output_file: str = None):
    """
    Extrait les étapes de test du JSON et crée un nouveau CSV avec colonnes séparées

    Args:
        input_file: Fichier CSV d'entrée (export Jira/Xray)
        output_file: Fichier CSV de sortie (optionnel, auto-généré si non fourni)
    """

    if not output_file:
        output_file = input_file.replace('.csv', '_steps_extracted.csv')

    logger.info(f"🚀 Début de l'extraction des étapes de test")
    logger.info(f"📁 Fichier d'entrée: {input_file}")
    logger.info(f"📁 Fichier de sortie: {output_file}")

    processed_tickets = 0
    total_steps = 0
    error_count = 0

    try:
        with open(input_file, 'r', newline='', encoding='utf-8') as csv_in, \
             open(output_file, 'w', newline='', encoding='utf-8') as csv_out:

            reader = csv.DictReader(csv_in)

            # Définir les colonnes essentielles à conserver
            essential_columns = [
                'Summary', 'Issue key', 'Issue id', 'Issue Type', 'Status',
                'Priority', 'Description', 'Component/s', 'Custom field (Test Repository Path)',
                'Reporter', 'Assignee', 'Created', 'Updated', 'Project key', 'Project name'
            ]

            # Vérifier quelles colonnes existent réellement
            available_columns = []
            for col in essential_columns:
                if col in reader.fieldnames:
                    available_columns.append(col)
                else:
                    logger.warning(f"⚠️ Colonne non trouvée: {col}")

            # Ajouter les nouvelles colonnes d'étapes
            output_fieldnames = available_columns + ['Action', 'Data', 'Expected Result']

            writer = csv.DictWriter(csv_out, fieldnames=output_fieldnames, quoting=csv.QUOTE_ALL)
            writer.writeheader()

            logger.info(f"📋 Colonnes conservées: {len(available_columns)}")
            logger.info(f"📋 Colonnes d'étapes ajoutées: Action, Data, Expected Result")
            
            for row_num, row in enumerate(reader, 1):
                try:
                    # Récupérer le JSON des étapes de test
                    steps_json = row.get('Custom field (Manual Test Steps)', '')
                    
                    # Parser le JSON
                    steps = parse_test_steps_json(steps_json)
                    
                    if steps:
                        # Créer une ligne pour chaque étape
                        for step in steps:
                            new_row = {}

                            # Copier seulement les colonnes essentielles
                            for col in available_columns:
                                new_row[col] = row.get(col, '')

                            # Ajouter les colonnes d'étapes
                            new_row['Action'] = step['action']
                            new_row['Data'] = step['data']
                            new_row['Expected Result'] = step['expected_result']

                            writer.writerow(new_row)
                            total_steps += 1
                    else:
                        # Pas d'étapes, créer une ligne vide pour les étapes
                        new_row = {}

                        # Copier seulement les colonnes essentielles
                        for col in available_columns:
                            new_row[col] = row.get(col, '')

                        new_row['Action'] = ''
                        new_row['Data'] = ''
                        new_row['Expected Result'] = ''

                        writer.writerow(new_row)
                    
                    processed_tickets += 1
                    
                    if processed_tickets % 50 == 0:
                        logger.info(f"📊 Traité {processed_tickets} tickets, {total_steps} étapes extraites...")
                        
                except Exception as e:
                    logger.error(f"❌ Erreur ligne {row_num}: {e}")
                    error_count += 1
                    continue
    
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'ouverture des fichiers: {e}")
        return False
    
    # Résumé final
    logger.info(f"✅ Extraction terminée!")
    logger.info(f"📊 Statistiques:")
    logger.info(f"   - Tickets traités: {processed_tickets}")
    logger.info(f"   - Étapes extraites: {total_steps}")
    logger.info(f"   - Erreurs: {error_count}")
    logger.info(f"📁 Fichier généré: {output_file}")

    # Générer le rapport automatique
    generate_execution_report(input_file, output_file, processed_tickets, total_steps, error_count)

    return True

def parse_test_steps_json(steps_json: str) -> List[Dict[str, str]]:
    """
    Parse le JSON des étapes de test et retourne une liste d'étapes
    
    Args:
        steps_json: Chaîne JSON contenant les étapes
        
    Returns:
        Liste de dictionnaires avec action, data, expected_result
    """
    if not steps_json or steps_json.strip() == '' or steps_json == '[]':
        return []
    
    try:
        # Parser le JSON
        steps_data = json.loads(steps_json)
        
        if not isinstance(steps_data, list):
            return []
        
        extracted_steps = []
        
        for step in steps_data:
            if isinstance(step, dict) and 'fields' in step:
                fields = step['fields']
                
                # Extraire les champs avec nettoyage basique
                action = clean_text(fields.get('Action', ''))
                data = clean_text(fields.get('Data', ''))
                expected_result = clean_text(fields.get('Expected Result', ''))
                
                extracted_steps.append({
                    'action': action,
                    'data': data,
                    'expected_result': expected_result
                })
        
        return extracted_steps
        
    except json.JSONDecodeError as e:
        logger.warning(f"⚠️ Erreur de parsing JSON: {e}")
        return []
    except Exception as e:
        logger.warning(f"⚠️ Erreur inattendue lors du parsing: {e}")
        return []

def clean_text(text: str) -> str:
    """
    Nettoie le texte en supprimant les caractères d'échappement basiques
    
    Args:
        text: Texte à nettoyer
        
    Returns:
        Texte nettoyé
    """
    if not text:
        return ''
    
    # Remplacer les caractères d'échappement courants
    text = text.replace('\\n', '\n')
    text = text.replace('\\t', '\t')
    text = text.replace('\u003d', '=')
    text = text.replace('""', '"')
    
    return text.strip()

def main():
    """Point d'entrée principal"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Extracteur d\'étapes de test Jira/Xray')
    parser.add_argument('input_file', help='Fichier CSV d\'export Jira/Xray')
    parser.add_argument('-o', '--output', help='Fichier de sortie (optionnel)')
    
    args = parser.parse_args()
    
    # Vérifier que le fichier d'entrée existe
    import os
    if not os.path.exists(args.input_file):
        logger.error(f"❌ Fichier non trouvé: {args.input_file}")
        return 1
    
    # Lancer l'extraction
    success = extract_test_steps(args.input_file, args.output)
    
    if success:
        print("\n🎉 Extraction réussie!")
        print("\n📋 PROCHAINES ÉTAPES:")
        print("1. Vérifiez le fichier généré")
        print("2. Importez-le dans votre nouveau projet Jira/Xray")
        print("3. Les étapes seront maintenant dans des colonnes séparées")
        return 0
    else:
        print("\n❌ Extraction échouée. Consultez les logs pour plus de détails.")
        return 1

def generate_execution_report(input_file: str, output_file: str, processed_tickets: int, total_steps: int, error_count: int):
    """Génère un rapport d'exécution automatique"""

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"execution_report_{timestamp}.txt"

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("🎯 RAPPORT D'EXÉCUTION - EXTRACTEUR ÉTAPES DE TEST JIRA/XRAY\n")
            f.write("=" * 70 + "\n\n")

            # Informations générales
            f.write(f"📅 Date/Heure: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
            f.write(f"📁 Fichier source: {input_file}\n")
            f.write(f"📁 Fichier généré: {output_file}\n")
            f.write(f"📁 Rapport: {report_file}\n\n")

            # Statistiques
            f.write("📊 STATISTIQUES D'EXÉCUTION:\n")
            f.write("-" * 40 + "\n")
            f.write(f"   • Tickets traités: {processed_tickets}\n")
            f.write(f"   • Étapes extraites: {total_steps}\n")
            f.write(f"   • Erreurs: {error_count}\n")

            success_rate = ((processed_tickets - error_count) / processed_tickets * 100) if processed_tickets > 0 else 0
            f.write(f"   • Taux de réussite: {success_rate:.1f}%\n\n")

            # Statut
            if error_count == 0:
                f.write("✅ STATUT: EXTRACTION RÉUSSIE\n")
            else:
                f.write("⚠️ STATUT: EXTRACTION AVEC ERREURS\n")

            f.write("\n🎯 PROCHAINES ÉTAPES:\n")
            f.write("-" * 25 + "\n")
            f.write("1. Vérifiez le fichier généré\n")
            f.write("2. Importez dans votre projet Jira/Xray\n")
            f.write("3. Mappez les colonnes Action/Data/Expected Result\n\n")

            # Colonnes conservées
            f.write("📋 COLONNES CONSERVÉES:\n")
            f.write("-" * 25 + "\n")
            f.write("   ✓ Summary, Issue key, Priority\n")
            f.write("   ✓ Description, Component/s\n")
            f.write("   ✓ Test Repository Path\n")
            f.write("   ✓ Action, Data, Expected Result\n")
            f.write("   ✓ Métadonnées complètes du ticket\n\n")

            f.write("🎉 Extraction terminée avec succès!\n")

        logger.info(f"📄 Rapport généré: {report_file}")

    except Exception as e:
        logger.error(f"❌ Erreur génération rapport: {e}")

if __name__ == '__main__':
    # Si appelé directement sans arguments, utiliser le fichier par défaut
    import sys
    if len(sys.argv) == 1:
        # Mode automatique avec le fichier Orange
        input_file = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
        if extract_test_steps(input_file):
            print("\n🎉 Extraction automatique réussie!")
            print(f"📁 Fichier généré: {input_file.replace('.csv', '_steps_extracted.csv')}")
            print(f"📄 Rapport automatique: execution_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        else:
            print("\n❌ Extraction échouée.")
    else:
        exit(main())
