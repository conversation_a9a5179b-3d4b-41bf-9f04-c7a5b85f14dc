# Outil de Migration Jira/Xray

Cet ensemble d'outils Python permet de reformater les exports CSV de Jira/Xray pour préserver la mise en forme et la police lors de l'import dans un nouveau projet.

## 🎯 Objectif

Lors de la migration de tickets de test Jira/Xray vers un nouveau projet, la colonne "Custom field (Manual Test Steps)" contient des données JSON complexes qui nécessitent un reformatage spécial pour préserver :
- La mise en forme (listes, titres, paragraphes)
- Les polices et styles
- La structure des étapes de test

## 📁 Fichiers du projet

### Scripts principaux
- **`jira_xray_migration_tool.py`** : Outil principal de migration
- **`validate_migration.py`** : Script de validation des résultats
- **`run_migration.py`** : Script d'exécution automatisée

### Scripts existants (versions précédentes)
- `export_xray_steps.py` : Extraction des étapes en colonnes séparées
- `export_xray_steps_cleaned.py` : Nettoyage et formatage HTML
- `export_xray_final.py` : Regroupement des étapes

## 🚀 Utilisation

### Méthode simple (recommandée)
```bash
python run_migration.py
```

Cette commande :
1. Traite automatiquement votre fichier CSV d'export
2. Génère un fichier migré avec formatage HTML
3. Produit un rapport de validation
4. Affiche un résumé complet

### Utilisation avancée

#### Migration manuelle
```bash
python jira_xray_migration_tool.py "JIRA for Orange 2025-07-01T12_00_00+0200.csv" -o output.csv -f html
```

#### Validation séparée
```bash
python validate_migration.py original.csv migrated.csv -o rapport.txt
```

## 🔧 Fonctionnalités

### Formatage des étapes de test
- **Conversion JSON → HTML** : Transforme les données JSON complexes en HTML structuré
- **Préservation du formatage** : Maintient les listes, titres, et mise en forme
- **Nettoyage des caractères** : Supprime les caractères parasites d'encodage

### Formats de sortie
- **HTML** (recommandé) : Formatage riche pour Jira
- **Séparé** : Format avec séparateurs pour traitement ultérieur

### Validation automatique
- Vérification du nombre de tickets
- Contrôle de la conversion des étapes
- Validation du formatage HTML
- Détection des erreurs

## 📊 Exemple de transformation

### Avant (JSON brut)
```json
[{"id":3763673,"fields":{"Action":"- normal case, change HLR/EPC HSS profile","Data":"","Expected Result":"Same data in SDM107 and SDM207"}}]
```

### Après (HTML formaté)
```html
<h4>Étape 1</h4>
<p><strong>Action:</strong> <ul><li>normal case, change HLR/EPC HSS profile</li></ul></p>
<p><strong>Résultat attendu:</strong> <p>Same data in SDM107 and SDM207</p></p>
```

## 📋 Structure du rapport de validation

Le rapport inclut :
- **Statistiques générales** : Nombre de tickets traités
- **Conversion des étapes** : Taux de réussite, erreurs détectées
- **Formatage HTML** : Validation des balises, tags utilisés
- **Recommandations** : Actions correctives si nécessaire

## 🛠️ Configuration

### Prérequis
- Python 3.6+
- Modules standard : `csv`, `json`, `re`, `argparse`, `os`, `logging`

### Personnalisation
Vous pouvez modifier les fonctions de formatage dans `jira_xray_migration_tool.py` :
- `clean_and_format_text()` : Logique de nettoyage
- `format_steps_for_import()` : Format de sortie des étapes

## 🔍 Résolution de problèmes

### Erreurs courantes
1. **Fichier non trouvé** : Vérifiez le chemin du fichier CSV
2. **Erreur d'encodage** : Le script gère UTF-8, UTF-8-BOM
3. **JSON malformé** : Les erreurs sont loggées et ignorées

### Logs
Les logs détaillés sont affichés pendant l'exécution :
- `INFO` : Progression normale
- `WARNING` : Problèmes non bloquants
- `ERROR` : Erreurs critiques

## 📈 Améliorations apportées

Par rapport aux scripts originaux :
- ✅ **Gestion d'erreurs robuste**
- ✅ **Validation automatique**
- ✅ **Formatage HTML amélioré**
- ✅ **Support des listes numérotées**
- ✅ **Nettoyage des caractères Unicode**
- ✅ **Rapport détaillé**
- ✅ **Interface en ligne de commande**

## 🎯 Prochaines étapes après migration

1. **Vérifiez le rapport** de validation généré
2. **Examinez le fichier migré** pour vous assurer que le formatage est correct
3. **Testez l'import** dans votre nouveau projet Jira
4. **Vérifiez la préservation** de la mise en forme dans l'interface Jira
5. **Ajustez si nécessaire** les paramètres de formatage

## 📞 Support

En cas de problème :
1. Consultez les logs détaillés
2. Vérifiez le rapport de validation
3. Examinez les fichiers d'exemple générés
4. Modifiez les paramètres de formatage si nécessaire
