# 🎯 Extracteur d'Étapes de Test Jira/Xray

## 📋 Description

Cet outil extrait les étapes de test manuelles depuis un export CSV Jira/Xray et les transforme en colonnes séparées pour faciliter l'import dans un nouveau projet.

### ✨ Fonctionnalités

- ✅ **Extraction JSON** : Parse les données de `Custom field (Manual Test Steps)`
- ✅ **Colonnes séparées** : C<PERSON>e `Action`, `Data`, `Expected Result`
- ✅ **Duplication intelligente** : Une ligne par étape de test
- ✅ **Colonnes essentielles** : Conserve Description, Priority, Component/s, Test Repository Path
- ✅ **Rapports automatiques** : Génère un rapport détaillé à chaque exécution
- ✅ **Validation** : Vérifie l'intégrité de l'extraction

## 🚀 Utilisation Rapide

### 1. Extraction principale
```bash
python extract_test_steps.py
```

### 2. Validation (optionnel)
```bash
python validate_extraction.py
```

### 3. Aperçu simplifié (optionnel)
```bash
python create_summary_view.py
```

## 📁 Structure des Fichiers

### 📥 Fichier d'entrée
- `JIRA for Orange 2025-07-01T12_00_00+0200.csv` - Export Jira/Xray original

### 📤 Fichiers générés
- `JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv` - **Fichier principal pour import**
- `summary_extracted_steps_v2.csv` - Aperçu simplifié
- `execution_report_YYYYMMDD_HHMMSS.txt` - Rapport d'exécution automatique

## 📊 Colonnes Conservées

### 🔧 Métadonnées du ticket
- Summary, Issue key, Issue id, Issue Type, Status
- **Priority** (100% remplie)
- **Description** (88.9% remplie)
- **Component/s** (100% remplie)
- **Custom field (Test Repository Path)** (88.9% remplie)
- Reporter, Assignee, Created, Updated
- Project key, Project name

### 🧪 Étapes de test
- **Action** - Actions à effectuer
- **Data** - Données de test
- **Expected Result** - Résultats attendus

## 🔄 Transformation

### Avant (JSON complexe)
```json
[{
  "id": 3763673,
  "fields": {
    "Action": "change HLR/EPC HSS profile...",
    "Data": "",
    "Expected Result": "Same data in SDM107 and SDM207"
  }
}]
```

### Après (Colonnes séparées)
```csv
Summary,Issue key,Priority,Component/s,Action,Data,Expected Result
BE Geo-Redundancy,SDMTESTP-289,Lowest,COMMON,"change HLR/EPC HSS profile...","","Same data in SDM107..."
```

## 📈 Statistiques Actuelles

- **16 tickets** traités
- **13 étapes** extraites avec succès
- **18 lignes** dans le fichier final
- **0 erreur** d'extraction
- **100% de réussite**

## 🛠️ Scripts Disponibles

| Script | Description | Sortie |
|--------|-------------|---------|
| `extract_test_steps.py` | **Principal** - Extraction complète | Fichier CSV + rapport |
| `validate_extraction.py` | Validation et statistiques | Rapport de validation |
| `create_summary_view.py` | Aperçu simplifié | CSV résumé |
| `analyze_columns.py` | Analyse des colonnes source | Statistiques colonnes |
| `final_report_v2.py` | Rapport final détaillé | Rapport complet |

## 🎯 Import dans Jira/Xray

### 1. Fichier à utiliser
```
JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv
```

### 2. Mapping des colonnes
- `Action` → champ Action de Xray
- `Data` → champ Data de Xray
- `Expected Result` → champ Expected Result de Xray

### 3. Avantages
- ✅ Structure claire et lisible
- ✅ Compatible import Jira/Xray standard
- ✅ Chaque étape sur ligne séparée
- ✅ Préservation métadonnées complètes
- ✅ Pas de formatage HTML complexe

## 🔍 Exemples de Données

### Ticket avec étapes multiples
```
SDMTESTP-289 | BE Geo-Redundancy | Lowest | COMMON
├── Étape 1: change HLR/EPC HSS profile...
└── Étape 2: synchronization outage...
```

### Résultat final
```
Ligne 1: SDMTESTP-289 | ... | change HLR/EPC HSS profile... | | Same data...
Ligne 2: SDMTESTP-289 | ... | synchronization outage... | | When synchronization...
```

## 📝 Logs et Rapports

Chaque exécution génère automatiquement :
- 📊 Statistiques d'extraction
- 🔍 Exemples de transformation
- ⚠️ Alertes et erreurs éventuelles
- 📁 Liste des fichiers générés
- ⏱️ Horodatage complet

## 🆘 Dépannage

### Problèmes courants
1. **Permission denied** → Fermer Excel si fichier ouvert
2. **Encoding errors** → Vérifier UTF-8 du fichier source
3. **JSON parse errors** → Vérifier format colonne Manual Test Steps

### Support
- Vérifier les logs d'exécution
- Consulter le rapport automatique
- Analyser les statistiques de validation

---

## 🎉 Résultat Final

**Mission accomplie !** Votre fichier CSV est prêt pour l'import dans le nouveau projet Jira/Xray avec les étapes de test extraites et organisées en colonnes séparées.
