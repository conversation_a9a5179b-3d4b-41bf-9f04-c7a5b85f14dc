#!/usr/bin/env python3
"""
Outil de migration Jira/Xray - Reformatage des tickets de test
Auteur: Assistant IA
Date: 2025-07-01

Ce script traite l'export CSV de Jira/Xray pour préparer l'import dans un nouveau projet
en préservant la mise en forme et la police des tickets de test.
"""

import csv
import json
import re
import argparse
import os
from typing import Dict, List, Any
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class JiraXrayMigrationTool:
    def __init__(self, input_file: str, output_file: str = None):
        self.input_file = input_file
        self.output_file = output_file or self._generate_output_filename()
        
    def _generate_output_filename(self) -> str:
        """Génère un nom de fichier de sortie basé sur le fichier d'entrée"""
        base_name = os.path.splitext(self.input_file)[0]
        return f"{base_name}_migrated.csv"
    
    def clean_and_format_text(self, text: str) -> str:
        """
        Nettoie les caractères parasites et transforme le Markdown-like en HTML
        pour que Xray/Jira affiche correctement les indentations et les titres.
        """
        if not text:
            return ''

        # Remplacer les caractères parasites et encodages problématiques
        text = text.replace('Â ', ' ')
        text = text.replace('Â', '')
        text = text.replace('ï¼Œ', ',')
        text = text.replace('\\n', '\n')
        text = text.replace('\\t', '\t')
        text = text.replace('\u003d', '=')
        text = text.replace('""', '"')
        text = text.replace('###', '### ')

        # Découper en lignes
        lines = text.splitlines()
        formatted_lines = []
        in_list = False
        in_ordered_list = False

        for line in lines:
            stripped = line.strip()

            # Titres Markdown -> <h1>, <h2>, <h3> etc.
            header_match = re.match(r'^(#{1,6})\s*(.*)', stripped)
            if header_match:
                level = len(header_match.group(1))
                content = header_match.group(2).strip()
                self._close_lists(formatted_lines, in_list, in_ordered_list)
                in_list = in_ordered_list = False
                formatted_lines.append(f'<h{level}>{content}</h{level}>')
                continue

            # Listes numérotées -> <ol><li>...</li></ol>
            numbered_match = re.match(r'^\d+\.\s*(.*)', stripped)
            if numbered_match:
                content = numbered_match.group(1).strip()
                if in_list:
                    formatted_lines.append('</ul>')
                    in_list = False
                if not in_ordered_list:
                    formatted_lines.append('<ol>')
                    in_ordered_list = True
                formatted_lines.append(f'<li>{content}</li>')
                continue

            # Listes à puces -> <ul><li>...</li></ul>
            if stripped.startswith('- ') or stripped.startswith('* '):
                content = stripped[2:].strip()
                if in_ordered_list:
                    formatted_lines.append('</ol>')
                    in_ordered_list = False
                if not in_list:
                    formatted_lines.append('<ul>')
                    in_list = True
                formatted_lines.append(f'<li>{content}</li>')
                continue

            # Gestion des indentations (sous-listes)
            indent_match = re.match(r'^(\s+)[-*]\s*(.*)', stripped)
            if indent_match:
                content = indent_match.group(2).strip()
                if not in_list:
                    formatted_lines.append('<ul>')
                    in_list = True
                formatted_lines.append(f'<li style="margin-left: 20px;">{content}</li>')
                continue

            # Si c'est une ligne vide
            if not stripped:
                self._close_lists(formatted_lines, in_list, in_ordered_list)
                in_list = in_ordered_list = False
                formatted_lines.append('<br>')
                continue

            # Sinon, texte brut (paragraphe)
            self._close_lists(formatted_lines, in_list, in_ordered_list)
            in_list = in_ordered_list = False
            
            # Préserver les formatages en gras et italique
            stripped = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', stripped)
            stripped = re.sub(r'\*(.*?)\*', r'<em>\1</em>', stripped)
            stripped = re.sub(r'`(.*?)`', r'<code>\1</code>', stripped)
            
            formatted_lines.append(f'<p>{stripped}</p>')

        # Fermer les listes si elles sont encore ouvertes
        self._close_lists(formatted_lines, in_list, in_ordered_list)

        return ''.join(formatted_lines)
    
    def _close_lists(self, formatted_lines: List[str], in_list: bool, in_ordered_list: bool):
        """Ferme les listes ouvertes"""
        if in_list:
            formatted_lines.append('</ul>')
        if in_ordered_list:
            formatted_lines.append('</ol>')
    
    def parse_manual_test_steps(self, steps_json: str) -> List[Dict[str, str]]:
        """
        Parse le JSON des étapes de test manuelles et retourne une liste structurée
        """
        if not steps_json or steps_json == '[]':
            return []
        
        try:
            steps = json.loads(steps_json)
            parsed_steps = []
            
            for step in steps:
                if 'fields' in step:
                    action = self.clean_and_format_text(step['fields'].get('Action', ''))
                    data = self.clean_and_format_text(step['fields'].get('Data', ''))
                    expected = self.clean_and_format_text(step['fields'].get('Expected Result', ''))
                    
                    parsed_steps.append({
                        'action': action,
                        'data': data,
                        'expected_result': expected,
                        'index': step.get('index', len(parsed_steps) + 1)
                    })
            
            return parsed_steps
            
        except json.JSONDecodeError as e:
            logger.warning(f"Erreur de parsing JSON pour les étapes de test: {e}")
            return []
    
    def format_steps_for_import(self, steps: List[Dict[str, str]], format_type: str = 'html') -> str:
        """
        Formate les étapes pour l'import selon le type demandé
        """
        if not steps:
            return ''
        
        if format_type == 'html':
            formatted_steps = []
            for i, step in enumerate(steps, 1):
                step_html = f'<h4>Étape {i}</h4>'
                if step['action']:
                    step_html += f'<p><strong>Action:</strong> {step["action"]}</p>'
                if step['data']:
                    step_html += f'<p><strong>Données:</strong> {step["data"]}</p>'
                if step['expected_result']:
                    step_html += f'<p><strong>Résultat attendu:</strong> {step["expected_result"]}</p>'
                formatted_steps.append(step_html)
            return ''.join(formatted_steps)
        
        elif format_type == 'separated':
            # Format avec séparateurs pour traitement ultérieur
            formatted_steps = []
            for step in steps:
                step_text = f"{step['action']} ||| {step['data']} ||| {step['expected_result']}"
                formatted_steps.append(step_text)
            return '\n---STEP_SEPARATOR---\n'.join(formatted_steps)
        
        return ''

    def process_csv(self, output_format: str = 'html'):
        """
        Traite le fichier CSV principal
        """
        logger.info(f"Début du traitement de {self.input_file}")
        
        processed_count = 0
        error_count = 0
        
        try:
            with open(self.input_file, 'r', newline='', encoding='utf-8') as csv_in, \
                 open(self.output_file, 'w', newline='', encoding='utf-8') as csv_out:
                
                reader = csv.DictReader(csv_in)
                
                # Préparer les en-têtes de colonnes
                fieldnames = list(reader.fieldnames)
                
                # Remplacer la colonne Manual Test Steps par une version formatée
                if 'Custom field (Manual Test Steps)' in fieldnames:
                    idx = fieldnames.index('Custom field (Manual Test Steps)')
                    fieldnames[idx] = 'Manual Test Steps (Formatted)'
                
                writer = csv.DictWriter(csv_out, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
                writer.writeheader()
                
                for row in reader:
                    try:
                        # Traiter les étapes de test manuelles
                        steps_json = row.get('Custom field (Manual Test Steps)', '')
                        steps = self.parse_manual_test_steps(steps_json)
                        formatted_steps = self.format_steps_for_import(steps, output_format)
                        
                        # Remplacer la colonne
                        if 'Custom field (Manual Test Steps)' in row:
                            del row['Custom field (Manual Test Steps)']
                        row['Manual Test Steps (Formatted)'] = formatted_steps
                        
                        # Nettoyer et formater d'autres champs texte importants
                        for field in ['Description', 'Summary']:
                            if field in row and row[field]:
                                row[field] = self.clean_and_format_text(row[field])
                        
                        writer.writerow(row)
                        processed_count += 1
                        
                        if processed_count % 100 == 0:
                            logger.info(f"Traité {processed_count} tickets...")
                            
                    except Exception as e:
                        logger.error(f"Erreur lors du traitement de la ligne {processed_count + 1}: {e}")
                        error_count += 1
                        continue
                
        except Exception as e:
            logger.error(f"Erreur lors de l'ouverture des fichiers: {e}")
            return False
        
        logger.info(f"Traitement terminé: {processed_count} tickets traités, {error_count} erreurs")
        logger.info(f"Fichier de sortie généré: {self.output_file}")
        return True

def main():
    parser = argparse.ArgumentParser(description='Outil de migration Jira/Xray')
    parser.add_argument('input_file', help='Fichier CSV d\'export Jira/Xray')
    parser.add_argument('-o', '--output', help='Fichier de sortie (optionnel)')
    parser.add_argument('-f', '--format', choices=['html', 'separated'], default='html',
                       help='Format de sortie pour les étapes de test')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        logger.error(f"Le fichier {args.input_file} n'existe pas")
        return 1
    
    tool = JiraXrayMigrationTool(args.input_file, args.output)
    success = tool.process_csv(args.format)
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())
