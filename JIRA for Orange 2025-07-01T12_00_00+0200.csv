Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Fix Version/s,Component/s,Due Date,Votes,Labels,Description,Environment,Watchers,Log Work,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Inward issue link (Cloners),Outward issue link (Cloners),Outward issue link (Defect),Outward issue link (Defect),Outward issue link (Defect),Outward issue link (Defect),Outward issue link (Defect),Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Custom field (ACAT),Custom field (Acceptance Criteria (deprecated)),Custom field (Actual Business Value),Custom field (Affects Build),Custom field (Ambition),Custom field (Analysing Date),Custom field (Approvers),Custom field (Assessment Date),Custom field (Automation status),Custom field (Basicat code),Custom field (Begin Date),Custom field (Benefit Hypothesis),Custom field (Blocking Reason),Custom field (Branch),Custom field (Budget),Custom field (Budget Code),Custom field (Budget Max (k€)),Custom field (Business Center),Custom field (Business Gain),Custom field (Business Owners),Custom field (Business Value),Custom field (CAPEX),Custom field (CDR/MC),Custom field (Carbone),Custom field (Category List),Custom field (Cause),Custom field (Change completion date),Custom field (Change risk (deprecated)),Custom field (Change start date),Custom field (Change type),Custom field (Collection),Custom field (Commited),Custom field (Complexity),Custom field (Conditions),Custom field (Confidentiality level),Custom field (Consequence),Custom field (Contextual description),Custom field (Contingence),Custom field (Contributor/s),Custom field (Cost),Custom field (Cost Of Delay),Custom field (Cucumber Scenario),Custom field (Cucumber Test Type),Custom field (Customer Impact),Customer Request Type,Custom field (Customer space),Custom field (Dataset values),Custom field (Dataset values),Custom field (Date MEP),Custom field (Date UAT),Demande SLA 16H,Demande SLA 16H simplified,Custom field (Devices),Custom field (Domain List),Durée de traitement,Durée de traitement simplified,Délai de Qualification,Délai de Qualification simplified,Délai de prise en charge,Délai de prise en charge simplified,Custom field (Effects),Custom field (End Date),Custom field (Entity List),Custom field (Environment),Custom field (Epic Color),Custom field (Epic Link),Custom field (Epic Name),Custom field (Epic Status),Custom field (Epic/Theme),Custom field (Estimated Gain),Custom field (Estimated Payback),Custom field (Evolution),Custom field (External Contributor/s),Custom field (External Link),Custom field (External issue ID),Custom field (FTE),Fermeture apres x jours,Fermeture apres x jours simplified,Custom field (Firmware Version),Custom field (First Analysing Transition),Custom field (First Archived Transition),Custom field (First Backlog Transition),Custom field (First Cancelled Transition),Custom field (First Deployed Transition),Custom field (First Done Transition),Custom field (First In Progress Transition),Custom field (First In review Transition),Custom field (First MVP Transition),Custom field (First Ready for Refinement Transition),Custom field (First Released Transition),Custom field (First Time to User),Custom field (First To Do Transition),Custom field (First key),Custom field (First-Name),Custom field (Fix Build),Custom field (Flagged),Custom field (Gain),Custom field (Generic Test Definition),Custom field (Go/noGo Date),Custom field (Groups),Custom field (Groups),Custom field (Impact),Custom field (Impacted Business List),Custom field (Impacted Entity),Custom field (Import),Custom field (Jira Project Lead),Custom field (Jira Project Type),Custom field (Job Size),Custom field (Last Backlog Status Date),Custom field (Last Comment (Auto)),Custom field (Last Deployed Status Date),Custom field (Last Implementation Review),Custom field (Last Milestone),Custom field (Last Milestone Date),Custom field (Last Released Status Date),Custom field (Last-Name),Custom field (Lead Time Agile (Days)),Custom field (Linked major incidents),Custom field (List Entity),Custom field (Loss Hypothesis),Custom field (MVP Macro Budget (K€)),Custom field (Macro estimation),Custom field (Mail),Custom field (Manual Test Steps),Custom field (MoSCoW),Custom field (Next committee),Custom field (Operational categorization),Custom field (Organizations),Custom field (Original story points),Custom field (Overcoast),Custom field (PDR Symbioz),Custom field (Parent),Custom field (Parent Key),Custom field (Parent Link),Custom field (Payback),Custom field (Penalties),Custom field (Pending reason),Custom field (Period),Custom field (Phase),Custom field (Plan Types),Custom field (Platform),Custom field (Pre-Condition Type),Custom field (Pre-Conditions association with a Test),Prise en compte,Prise en compte simplified,Custom field (Probability),Custom field (Product categorization),Custom field (Program),Custom field (Project Cycle (Days)),Custom field (Project Entity),Custom field (Project Owner),Custom field (QC),Custom field (Qualification Date),Custom field (Rank),Custom field (Reached (%)),Custom field (Recharging),Custom field (Ref. Project CARTO),Custom field (Ref. RGP),Custom field (Ref. Symbioz),Custom field (Reference Code),Custom field (Release Date),Custom field (Request participants),Custom field (Requirement Status),Resolution Time SLA,Resolution Time SLA simplified,Custom field (Responsable Solution),Response Time SLA,Response Time SLA simplified,Custom field (Result),Custom field (Review date),Custom field (Revision),Custom field (Risk Reduction Opportunity),Satisfaction score (out of 5),Custom field (Scoring),Custom field (Script Gain Type),Custom field (Severity),Custom field (Softwares),Custom field (Source),Custom field (Specificities),Sprint,Custom field (Start Date),Custom field (Steps Count),Custom field (Story Points),Custom field (Structure Index Monitor),Custom field (Sub root cause),Custom field (Support),Custom field (T-1),Custom field (T-2),Custom field (T-Shirt Size),Custom field (T0),Custom field (T1),Custom field (T2),Custom field (T3),Custom field (T3Cl),Custom field (T4),Custom field (Target),Custom field (Target end),Custom field (Target project),Custom field (Target start),Custom field (Team),Custom field (Team List),"Temps d&#39;attribution","Temps d&#39;attribution simplified",Temps première réponse,Temps première réponse simplified,Custom field (Test Count),Custom field (Test Environments),Custom field (Test Execution Defects),Custom field (Test Execution Status),Custom field (Test Plan),Custom field (Test Plan Status),Custom field (Test Plan Tests Filter),Custom field (Test Plans associated with a Test),Custom field (Test Repository Path),Custom field (Test Set Status),Custom field (Test Sets association with a Test),Custom field (Test Type),Custom field (TestRunStatus),Custom field (Tests associated with a Test Plan),Custom field (Tests association with a Pre-Condition),Custom field (Tests association with a Test Execution),Custom field (Tests association with a Test Set),Custom field (Time Criticality),Time to close after resolution,Time to close after resolution simplified,Time to first response,Time to first response simplified,Time to resolution,Time to resolution simplified,Custom field (TimeRecup (deprecated)),Custom field (Type),Custom field (Urgency),Custom field (User Activity),Custom field (Validation Status),Custom field (Visibility List),Custom field (WSJF),Custom field (WSJF (Auto)),Custom field (Workaround (deprecated)),Custom field (committee),Comment
Documentation availability and conformance,SDMTESTP-290,8725090,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Medium,,,<EMAIL>,<EMAIL>,06/Jun/25 2:16 PM,10/Jun/25 3:28 PM,01/Jul/25 11:58 AM,,,,COMMON,,0,,Report of error in documentation,,<EMAIL>,,,,,,,,,,,,ZTESDM-540,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SDMTESTP-290,,,,,,,,,,,,,,software,,,"<ul>
<li><ins>SWx RESET testcase :</ins> update of the documentation (EPC AAA RESET) in order to indicate that the SWx message is PPR with PPR_FLAG Reset Indication set, and not a RSR message</li>
<li>SDS Policy testcase : update of the documentation in order to indicate the need of feature activation (17005), and explanation of VOWIFIPRE parameter in user library</li>
</ul>
",,,,,,,,,,,,,,[],,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0qr4w:",,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8725090,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/DOCUMENTATION,,,Manual,FAIL,,,,,,,,,,,,,,,,,,,,,,"06/Jun/25 2:46 PM;<EMAIL>;* +SWx RESET testcase :+ update of the documentation (EPC AAA RESET) in order to indicate that the SWx message is PPR with PPR_FLAG Reset Indication set, and not a RSR message
 * SDS Policy testcase : update of the documentation in order to indicate the need of feature activation (17005), and explanation of VOWIFIPRE parameter in user library;;;"
BE Geo-Redundancy,SDMTESTP-289,8724318,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Lowest,,,<EMAIL>,<EMAIL>,06/Jun/25 11:19 AM,09/Jun/25 12:11 PM,12/Jun/25 11:18 AM,,,,COMMON,,0,,"'- Normal case:\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.\n- synchronisation outage :\n	- Disable the synchronisation between 2 SDM , it could be done by either or following command:\n		- ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK\n		- SET NFCFG:NFID=207,NFState=""BLOCK""\n		- SYNA\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\n	- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207\n	- enable the  synchronization between 2 SDM, by either or following command:\n		- SET NFCFG:NFID=107,NFState=""NORMAL""\n		- SET NFCFG:NFID=207,NFState=""NORMAL""\n		- SYNA\n	- check data in webagent in SDM207, the changed data could be seen.",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SDMTESTP-289,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3763673,""index"":1,""fields"":{""Action"":""- normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there."",""Data"":"""",""Expected Result"":""Same data in SDM107 and SDM207""},""attachments"":[],""testVersionId"":1065036},{""id"":3763674,""index"":2,""fields"":{""Action"":""synchronization outage :\\n- \\n\t- disable the synchrnozation between 2 SDM , it could be done by either or following command:\\n\t\t- SET NFCFG:NFID\u003d107,NFState\u003d\""BLOCK\""\\n\t\t- SET NFCFG:NFID\u003d207,NFState\u003d\""BLOCK\""\\n\t- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\\n\t- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207\\n\t\t- SET NFCFG:NFID\u003d107,NFState\u003d\""NORMAL\""\\n\t\t- SET NFCFG:NFID\u003d207,NFState\u003d\""NORMAL\""\\n\t- enable the  synchrnozation between 2 SDM, by either or following command:\\n\t- check data in webagent in SDM207, the changed data could be seen."",""Data"":"""",""Expected Result"":""When synchronization is disabled :\n\nThe changed data in SDM107 is not seen in SDM207\n\nWhen synchronization is enabled between the two SDMs:\n\nThe changed data in SDM107 is seen in SDM207""},""attachments"":[],""testVersionId"":1065036}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0qk5c:",,,,,,,,,,,,,,,,,,,,,,,,,,,,2.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8724318,""testStatuses"":[]}",,,,,,,,Manual,TODO,,,,,,,,,,,,,,,,,,,,,,
EPC HSS VoLTE SRVCC,SDMTESTP-286,8700061,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,03/Jun/25 9:17 AM,06/Jun/25 5:27 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,"SRVCC and its variants are network based mechanisms by which voice call continuity can be assured from IMS voice over PS access and CS voice service when the UE is capable of transmitting/receiving on only one of those access networks at a given time. they are intermediate solutions to meet the scenarios that VoLTE services can't assure voice continuity due to insufficient LTE coverage at the beginning of LTE rollout and wide legacy CS networks' coverage can be resorted to. Since LTE and VoLTE services are a fundamental part of next-generation mobile networks, SRVCC is a key capability while LTE coverage continues to be spotty.
 

SRVCC is defined in 3GPP TS 23.216 for voice call continuity from LTEtoCS (CircuitSwitched), SRVCC allows IMS session continuity (specified in 3GPP TS 23.237) when the UE has a single radio, thus only one RAT can be active at one time.
When moving out from IMS Voice capable LTE coverage, SRVCC allows voice continuity via handover to 2G/3G CS. It is considered an important business advantage for operators since it allows a superior VoIP service that cannot be matched by third party voice application providers until LTE coverage is perfected.

As an SRVCC-capable mobile engaged in a voice call determines that it is moving away from LTE coverage, it notifies the LTE network. The LTE network determines that the voice call needs to be moved to the legacy circuit domain. It notifies the MSC server of the need to switch the voice call from the packet to the circuit domain and initiates a handover of the LTE voice bearer to the circuit network. In this step, MME shall carry a STN-SR number identifying SCCAS in IMS core and C-MSISDN (correlation MSISDN to find the original call leg in IMS), which is downloaded from EPC HSS as the user initiates attach procedure. The MSC server establishes a bearer path for the mobile in the legacy network and notifies the IMS core that the mobile's call leg is moving from the packet to the circuit domain. The circuit-packet function in the IMS core then performs the necessary interworking functions. When the mobile arrive on-channel in the legacy network, it switches its internal voice processing from VoIP to legacy-circuit voice, and the call continues.",,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:19 PM;<EMAIL>;20250509_S6a_ULR-SRVCC_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884442/20250509_S6a_ULR-SRVCC_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SDMTESTP-286,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757874,""index"":1,""fields"":{""Action"":""- User A is provisionned with STN-SR value in his profile\n- User A initiate a S6a MME registration\n- MME support the SRVCC capability\n- MME send a ULR request to the EPC-HSS"",""Data"":"""",""Expected Result"":""- The EPC-HSS reply with ULA response that contain the STN-SR provisionned""},""attachments"":[],""testVersionId"":1062556}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0mh1s:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8700061,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
HLR Any Time Interrogation - CS,SDMTESTP-276,8697580,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:19 PM,06/Jun/25 5:41 PM,01/Jul/25 12:00 PM,,,,HLR,,0,,With this procedure a network node can acquire the subscriber current location information and state information from the HLR,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:16 PM;<EMAIL>;20250515_MAP_ATI_CS_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884436/20250515_MAP_ATI_CS_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-18,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757257,""index"":1,""fields"":{""Action"":""|- Subscriber A is registered under VLR\n- A network node (gsmSCF) send a ATI request for CS information retreival from the HLR|"",""Data"":"""",""Expected Result"":""- On receipt of ATI request, the HLR initiate an PSI request to the VLR\n- On receipt of PSI response the HLR return the ATI response with CS information requested""},""attachments"":[],""testVersionId"":1062322}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m228:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697580,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
 IMS HSS T-ADS SCC-AS retreive CSRN via Sh,SDMTESTP-284,8697577,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:16 PM,06/Jun/25 5:37 PM,12/Jun/25 11:15 AM,,,,IMS-HSS,,0,,,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:24 PM;<EMAIL>;20250521_Sh_UDR_T-ADS_SCCAS-retreive-CSRN-via-Sh_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884447/20250521_Sh_UDR_T-ADS_SCCAS-retreive-CSRN-via-Sh_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-17,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,[],,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m1ww:",,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697577,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
 IMS HSS T-ADS SCC-AS retreive TADS via Sh,SDMTESTP-275,8697576,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:16 PM,06/Jun/25 5:41 PM,06/Jun/25 5:41 PM,,,,IMS-HSS,,0,,,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:24 PM;<EMAIL>;20250520_Sh_UDR_T-ADS_SCCAS-retreive-TADS-via-Sh_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884448/20250520_Sh_UDR_T-ADS_SCCAS-retreive-TADS-via-Sh_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-16,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,[],,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m1w0:",,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697576,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
IMS HSS T-ADS IMS Domain Prefered by HSS,SDMTESTP-279,8697399,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:10 PM,06/Jun/25 5:40 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,HSS policy is defined to operate the call over IMS domain,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:22 PM;<EMAIL>;20250507_MAP_T-ADS_HSS-Policy-IMS-Preferred_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884445/20250507_MAP_T-ADS_HSS-Policy-IMS-Preferred_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-15,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757140,""index"":1,""fields"":{""Action"":""|- user A is attached in CS domain\n- user A is attached in IMS domain\n- HSS policy domain selection policy is defined as IMS prefered\n\n- GMSC a SRI for user A|"",""Data"":"""",""Expected Result"":""- HSS reply with IMRN number""},""attachments"":[],""testVersionId"":1062317}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m16o:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697399,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
IMS HSS T-ADS CS Domain Prefered by HSS,SDMTESTP-280,8697398,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:10 PM,06/Jun/25 5:40 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,HSS policy is defined to operate the call over CS domain,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:23 PM;<EMAIL>;20250509_MAP_T-ADS_HSS-Policy-CS-Preferred_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884446/20250509_MAP_T-ADS_HSS-Policy-CS-Preferred_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-14,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757135,""index"":1,""fields"":{""Action"":""|- user A is attached in CS domain\n- user A is attached in IMS domain\n- HSS policy domain selection policy is defined as CS prefered\n\n- GMSC a SRI for user A|"",""Data"":"""",""Expected Result"":""- HSS reply with MRSN number""},""attachments"":[],""testVersionId"":1062316}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m15k:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697398,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
EPC HSS VoLTE eSRVCC,SDMTESTP-282,8697395,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 4:09 PM,06/Jun/25 5:38 PM,12/Jun/25 11:22 AM,,,,IMS-HSS,,0,,"To minimize the voice interruption during the handover to CS if the Home IMS is located far away from the serving LTE network, A Rel-10 IMS HPLMN/VPLMN model (eSRVCC) introduces ATCF in VPLMN as the anchor of IMS session, instead of SCC AS like before. Since the new anchor is in VPLMN, its STN-SR is dynamic. To ensure that the MSC Server selects the correct ATCF during SRVCC procedure, the dynamic STN-SR pointing to the ATCF shall be provided to the MME before SRVCC procedure is triggered.

The ATCF shall allocate the its dynamic STN-SR when the user performs initial registration in the IMS. The STN-SR shall be provided through IMS and via third-party registration to the SCC AS. The SCC AS shall further provide the STN-SR to the HSS if the received STN-SR is different from the existing one that has been set, which in turn shall update the MME/SGSN. If requested the MME/SGSN shall indicate to the HSS if the UE has SRVCC capability. The SCC AS may inform the ATCF about the SRVCC capability of the UE.",,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:21 PM;<EMAIL>;20250519_Sh_PUR_SCCAS-update-STN-SR-via-S6a_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884444/20250519_Sh_PUR_SCCAS-update-STN-SR-via-S6a_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-13,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757133,""index"":1,""fields"":{""Action"":""- User A is provisionned with STN-SR value in his profile\n- User A initiate a S6a MME registration\n- MME support the SRVCC capability\n- MME send a ULR request to the EPC-HSS"",""Data"":"""",""Expected Result"":""- The EPC-HSS reply with ULA response that contain the STN-SR provisionned""},""attachments"":[],""testVersionId"":1062315}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m13s:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697395,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
SWx_Reset,SDMTESTP-277,8697369,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 3:57 PM,06/Jun/25 5:41 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,Test EPC non-3GPP access AAA Reset procedure,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:26 PM;<EMAIL>;20250526_SWx_Reset-HSS_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884454/20250526_SWx_Reset-HSS_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-11,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757109,""index"":1,""fields"":{""Action"":""|- From OAM provision a user Reset procedure\n- The HSS send a PPR request with PPR-Flag that contain Reset-Indicator set|"",""Data"":"""",""Expected Result"":""- The flag in PPR request with PPR-Flag contain Reset-Indicator set""},""attachments"":[],""testVersionId"":1062309}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0m0lk:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8697369,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
SWx_SAR,SDMTESTP-283,8696973,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 3:50 PM,06/Jun/25 5:38 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,Test EPC non-3GPP access Server Assignment Request,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:26 PM;<EMAIL>;20250525_SWx_SAR_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884453/20250525_SWx_SAR_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-10,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,[],,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0lzk8:",,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8696973,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
SWx_PPR,SDMTESTP-281,8696953,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 3:50 PM,06/Jun/25 5:39 PM,01/Jul/25 12:00 PM,,,,IMS-HSS,,0,,Test EPC non-3GPP access Push Profile Request,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:25 PM;<EMAIL>;20250526_SWx_PPR_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884452/20250526_SWx_PPR_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-9,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,[],,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0lzfc:",,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8696953,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
 SWx_MAR,SDMTESTP-274,8696919,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 3:40 PM,06/Jun/25 5:42 PM,26/Jun/25 10:11 AM,,,,IMS-HSS,,0,,Test EPC non-3GPP access Multimedia Authentication Request,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:25 PM;<EMAIL>;20250525_SWx_MAR_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884451/20250525_SWx_MAR_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-8,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757058,""index"":1,""fields"":{""Action"":""- UE  start authentication procedure.\n- The 3GPP AAA send a MAR message (EAP-AKA algorithm)"",""Data"":"""",""Expected Result"":""- The HSS reply with MAA that contain :\n   - 3GPP-SIP Authenticate\n   - 3GPP-SIP Authorization\n   - Confidentiality-Key\n   - Integrity-Key""},""attachments"":[],""testVersionId"":1062293}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0lytk:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8696919,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
HLR Any Time Interrogation - PS,SDMTESTP-285,8696597,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Low,,,<EMAIL>,<EMAIL>,02/Jun/25 3:32 PM,06/Jun/25 5:30 PM,12/Jun/25 11:27 AM,,,,HLR,,0,,With this procedure a network node can acquire the subscriber current location information and state information from the HSS,,<EMAIL>,,,,,,,,,,,,,,,,,"06/Jun/25 1:16 PM;<EMAIL>;20250519_MAP_ATI_PS_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884437/20250519_MAP_ATI_PS_OK.pcapng",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SANTSDM-7,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3757053,""index"":1,""fields"":{""Action"":""|- Subscriber A is registered under MME\n- A network node (gsmSCF) send a ATI request for PS information retreival from the HSS|"",""Data"":"""",""Expected Result"":""- On receipt of ATI request, the HSS initiate an IDR request to the MME\n- On receipt of IDA response the HSS return the ATI response with PS information requested""},""attachments"":[],""testVersionId"":1062274}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0ly5s:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8696597,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
BE Geo-Redundancy,SDMTESTP-271,8674776,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Medium,,,<EMAIL>,<EMAIL>,27/May/25 4:49 PM,20/Jun/25 10:15 PM,06/Jun/25 5:42 PM,,,,COMMON,,0,,"# Normal case:
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there. 
 # synchronisation outage : 
 ## Disable the synchronisation between 2 SDM , it could be done by either or following command:
 ### ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK
 ### SET NFCFG:NFID=207,NFState=""BLOCK""
 ### SYNA
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107
 ## login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
 ## enable the  synchronization between 2 SDM, by either or following command:
 ###  SET NFCFG:NFID=107,NFState=""NORMAL""
 ### SET NFCFG:NFID=207,NFState=""NORMAL""
 ### SYNA
 ## check data in webagent in SDM207, the changed data could be seen.",,<EMAIL>,,,,,,,,,,,,ZTESDM-552,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SDMTESTP-271,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3752326,""index"":1,""fields"":{""Action"":""# normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there."",""Data"":"""",""Expected Result"":""Same data in SDM107 and SDM207""},""attachments"":[],""testVersionId"":1059935},{""id"":3752327,""index"":2,""fields"":{""Action"":""synchronization outage :    \n # \n ## disable the synchrnozation between 2 SDM , it could be done by either or following command:\n ### SET NFCFG:NFID\u003d107,NFState\u003d\""BLOCK\""\n ### SET NFCFG:NFID\u003d207,NFState\u003d\""BLOCK\""\n ##  change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\n ## login web agent in SDM207, the changed data in SDM107 is not seen in SDM207\n ### SET NFCFG:NFID\u003d107,NFState\u003d\""NORMAL\""\n ### SET NFCFG:NFID\u003d207,NFState\u003d\""NORMAL\""\n ## enable the  synchrnozation between 2 SDM, by either or following command:\n ## check data in webagent in SDM207, the changed data could be seen."",""Data"":"""",""Expected Result"":""When synchronization is disabled :\n\nThe changed data in SDM107 is not seen in SDM207\n\nWhen synchronization is enabled between the two SDMs:\n\nThe changed data in SDM107 is seen in SDM207""},""attachments"":[],""testVersionId"":1059935}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0iago:",,,,,,,,,,,,,,,,,,,,,,,,,,,,2.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8674776,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/RESILIENCY Testcases/Geographical Redundancy,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,
FE Geo Redundancy,SDMTESTP-270,8674752,Test,To Do,SDMTESTP,SDM TESTPLAN,software,<EMAIL>,,,Medium,,,<EMAIL>,<EMAIL>,27/May/25 4:40 PM,03/Jun/25 6:36 PM,06/Jun/25 5:42 PM,,,,COMMON,,0,,"FE from SDM107 and SDM 207 are naturely working in load-sharing mode without need any configuration.  which means that, no matter message is sent to sdm107 or sdm207,   it could and should be processed successfully.   

BE from SDM107 and SDM 207 more complicated:  in BE level, both BE is working on load-sharing mode  but with bit of difference with the FE:
 * for reading message such as SRI, SRIforSMS,    all BE shares this type of traffic.   FE could access its own BE and complete service processing.
 * for modifying messages such as updatelocation,    DSA clusters in BE working in active-standby mode.   For the testbed case, there are 2 dsa clusters and  primary dsa node in all dsa clusters are  in SDM107,  then  for write message,  in normal case, no matter it is sent to FE in SDM107 or FE in SDM 207, then eventually, it is processed in primary DSA node in SDM107.    
 * message to SDM107,  FE direct applies change on primary node 1402 in SDM107, then changed data is sychronized to node 2402 in SDM207. 
 * messsage to SDM207, FE in SDM207 also applies change on node 1402 in SDM107, then changed data is sychronized to node 2402 in SDM207. 


Architecture of testbed :

!image-2025-05-27-16-59-57-197.png!",,<EMAIL>,,,,,,,,,,,,,,,,,"27/May/25 4:59 PM;<EMAIL>;image-2025-05-27-16-59-57-197.png;https://portail.agir.orange.com/secure/attachment/3855396/image-2025-05-27-16-59-57-197.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,"{""params"":[]}","{""params"":[]}",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,SDMTESTP-270,,,,,,,,,,,,,,software,,,,,,,,,,,,,,,,,"[{""id"":3752305,""index"":1,""fields"":{""Action"":""FE Geo-Redundancy test. \n # sends all traffic to SDM107 and it is supposed to be processed successfully. \n # same traffic to SDM207,  and  it is supposed to be processed successfully."",""Data"":"""",""Expected Result"":""# traffic to SDM107 is processed successfully. \n # traffic to SDM207 is processed successfully.""},""attachments"":[],""testVersionId"":1059926}]",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"0|j0i9z4:",,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,,,,,,"{""issueId"":8674752,""testStatuses"":[]}",,,,,/SDM COMMON TESTCASES/RESILIENCY Testcases/Geographical Redundancy,,,Manual,PASS,,,,,,,,,,,,,,,,,,,,,,