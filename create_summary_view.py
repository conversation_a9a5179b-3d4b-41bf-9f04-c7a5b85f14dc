#!/usr/bin/env python3
"""
Créer un aperçu simplifié du fichier extrait pour vérification
"""

import csv

def create_summary_view():
    """Crée un fichier CSV simplifié avec seulement les colonnes essentielles"""
    
    input_file = "JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv"
    output_file = "summary_extracted_steps_v2.csv"
    
    print("📋 Création d'un aperçu simplifié...")
    
    # Colonnes à conserver pour l'aperçu
    essential_columns = [
        'Summary', 'Issue key', 'Issue Type', 'Status', 'Priority',
        'Description', 'Component/s', 'Custom field (Test Repository Path)',
        'Action', 'Data', 'Expected Result'
    ]
    
    rows_written = 0
    
    with open(input_file, 'r', encoding='utf-8') as f_in, \
         open(output_file, 'w', newline='', encoding='utf-8') as f_out:
        
        reader = csv.DictReader(f_in)
        writer = csv.DictWriter(f_out, fieldnames=essential_columns, quoting=csv.QUOTE_ALL)
        
        writer.writeheader()
        
        for row in reader:
            # Créer une ligne simplifiée
            simplified_row = {}
            for col in essential_columns:
                value = row.get(col, '')
                # Limiter la longueur pour la lisibilité
                if col in ['Action', 'Expected Result', 'Description'] and len(value) > 80:
                    value = value[:77] + '...'
                elif col == 'Summary' and len(value) > 40:
                    value = value[:37] + '...'
                elif col == 'Component/s' and len(value) > 15:
                    value = value[:12] + '...'
                elif col == 'Custom field (Test Repository Path)' and len(value) > 30:
                    value = value[:27] + '...'
                simplified_row[col] = value
            
            writer.writerow(simplified_row)
            rows_written += 1
    
    print(f"✅ Aperçu créé: {output_file}")
    print(f"📊 {rows_written} lignes écrites")
    
    # Afficher quelques exemples
    print(f"\n📋 APERÇU DES PREMIÈRES LIGNES:")
    print("-" * 120)
    
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        print(f"{'Issue Key':<12} {'Summary':<25} {'Priority':<8} {'Component':<12} {'Action':<30} {'Expected':<25}")
        print("-" * 120)

        for i, row in enumerate(reader):
            if i >= 10:  # Limiter à 10 lignes
                break

            issue_key = row['Issue key'][:11]
            summary = row['Summary'][:24]
            priority = row.get('Priority', '')[:7]
            component = row.get('Component/s', '')[:11]
            action = row['Action'][:29] if row['Action'] else '(vide)'
            expected = row['Expected Result'][:24] if row['Expected Result'] else '(vide)'

            print(f"{issue_key:<12} {summary:<25} {priority:<8} {component:<12} {action:<30} {expected:<25}")
    
    print("-" * 120)
    print(f"\n💡 Fichier complet disponible: {output_file}")

if __name__ == '__main__':
    create_summary_view()
