#!/usr/bin/env python3
"""
Créer un aperçu simplifié du fichier extrait pour vérification
"""

import csv

def create_summary_view():
    """Crée un fichier CSV simplifié avec seulement les colonnes essentielles"""
    
    input_file = "JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv"
    output_file = "summary_extracted_steps.csv"
    
    print("📋 Création d'un aperçu simplifié...")
    
    # Colonnes à conserver pour l'aperçu
    essential_columns = [
        'Summary', 'Issue key', 'Issue Type', 'Status',
        'Action', 'Data', 'Expected Result'
    ]
    
    rows_written = 0
    
    with open(input_file, 'r', encoding='utf-8') as f_in, \
         open(output_file, 'w', newline='', encoding='utf-8') as f_out:
        
        reader = csv.DictReader(f_in)
        writer = csv.DictWriter(f_out, fieldnames=essential_columns, quoting=csv.QUOTE_ALL)
        
        writer.writeheader()
        
        for row in reader:
            # Créer une ligne simplifiée
            simplified_row = {}
            for col in essential_columns:
                value = row.get(col, '')
                # Limiter la longueur pour la lisibilité
                if col in ['Action', 'Expected Result'] and len(value) > 100:
                    value = value[:97] + '...'
                elif col == 'Summary' and len(value) > 50:
                    value = value[:47] + '...'
                simplified_row[col] = value
            
            writer.writerow(simplified_row)
            rows_written += 1
    
    print(f"✅ Aperçu créé: {output_file}")
    print(f"📊 {rows_written} lignes écrites")
    
    # Afficher quelques exemples
    print(f"\n📋 APERÇU DES PREMIÈRES LIGNES:")
    print("-" * 120)
    
    with open(output_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        print(f"{'Issue Key':<15} {'Summary':<30} {'Action':<40} {'Expected Result':<30}")
        print("-" * 120)
        
        for i, row in enumerate(reader):
            if i >= 10:  # Limiter à 10 lignes
                break
                
            issue_key = row['Issue key'][:14]
            summary = row['Summary'][:29]
            action = row['Action'][:39] if row['Action'] else '(vide)'
            expected = row['Expected Result'][:29] if row['Expected Result'] else '(vide)'
            
            print(f"{issue_key:<15} {summary:<30} {action:<40} {expected:<30}")
    
    print("-" * 120)
    print(f"\n💡 Fichier complet disponible: {output_file}")

if __name__ == '__main__':
    create_summary_view()
