﻿Summary;Issue key;Issue id;Issue Type;Status;Project key;Project name;Project type;Project lead;Project description;Project url;Priority;Resolution;Assignee;Reporter;Creator;Created;Updated;Last Viewed;Resolved;Affects Version/s;Fix Version/s;Component/s;Due Date;Votes;Labels;Description;Environment;Watchers;Log Work;Original Estimate;Remaining Estimate;Time Spent;Work Ratio;Σ Original Estimate;Σ Remaining Estimate;Σ Time Spent;Security Level;Inward issue link (Cloners);Outward issue link (Cloners);Outward issue link (Defect);Outward issue link (Defect)_1;Outward issue link (Defect)_2;Outward issue link (Defect)_3;Outward issue link (Defect)_4;Attachment;Attachment_5;Attachment_6;Attachment_7;Attachment_8;Attachment_9;Attachment_10;Attachment_11;Attachment_12;Custom field (ACAT);Custom field (Acceptance Criteria (deprecated));Custom field (Actual Business Value);Custom field (Affects Build);Custom field (Ambition);Custom field (Analysing Date);Custom field (Approvers);Custom field (Assessment Date);Custom field (Automation status);Custom field (Basicat code);Custom field (Begin Date);Custom field (Benefit Hypothesis);Custom field (Blocking Reason);Custom field (Branch);Custom field (Budget);Custom field (Budget Code);Custom field (Budget Max (k€));Custom field (Business Center);Custom field (Business Gain);Custom field (Business Owners);Custom field (Business Value);Custom field (CAPEX);Custom field (CDR/MC);Custom field (Carbone);Custom field (Category List);Custom field (Cause);Custom field (Change completion date);Custom field (Change risk (deprecated));Custom field (Change start date);Custom field (Change type);Custom field (Collection);Custom field (Commited);Custom field (Complexity);Custom field (Conditions);Custom field (Confidentiality level);Custom field (Consequence);Custom field (Contextual description);Custom field (Contingence);Custom field (Contributor/s);Custom field (Cost);Custom field (Cost Of Delay);Custom field (Cucumber Scenario);Custom field (Cucumber Test Type);Custom field (Customer Impact);Customer Request Type;Custom field (Customer space);Custom field (Dataset values);Custom field (Dataset values)_13;Custom field (Date MEP);Custom field (Date UAT);Demande SLA 16H;Demande SLA 16H simplified;Custom field (Devices);Custom field (Domain List);Durée de traitement;Durée de traitement simplified;Délai de Qualification;Délai de Qualification simplified;Délai de prise en charge;Délai de prise en charge simplified;Custom field (Effects);Custom field (End Date);Custom field (Entity List);Custom field (Environment);Custom field (Epic Color);Custom field (Epic Link);Custom field (Epic Name);Custom field (Epic Status);Custom field (Epic/Theme);Custom field (Estimated Gain);Custom field (Estimated Payback);Custom field (Evolution);Custom field (External Contributor/s);Custom field (External Link);Custom field (External issue ID);Custom field (FTE);Fermeture apres x jours;Fermeture apres x jours simplified;Custom field (Firmware Version);Custom field (First Analysing Transition);Custom field (First Archived Transition);Custom field (First Backlog Transition);Custom field (First Cancelled Transition);Custom field (First Deployed Transition);Custom field (First Done Transition);Custom field (First In Progress Transition);Custom field (First In review Transition);Custom field (First MVP Transition);Custom field (First Ready for Refinement Transition);Custom field (First Released Transition);Custom field (First Time to User);Custom field (First To Do Transition);Custom field (First key);Custom field (First-Name);Custom field (Fix Build);Custom field (Flagged);Custom field (Gain);Custom field (Generic Test Definition);Custom field (Go/noGo Date);Custom field (Groups);Custom field (Groups)_14;Custom field (Impact);Custom field (Impacted Business List);Custom field (Impacted Entity);Custom field (Import);Custom field (Jira Project Lead);Custom field (Jira Project Type);Custom field (Job Size);Custom field (Last Backlog Status Date);Custom field (Last Comment (Auto));Custom field (Last Deployed Status Date);Custom field (Last Implementation Review);Custom field (Last Milestone);Custom field (Last Milestone Date);Custom field (Last Released Status Date);Custom field (Last-Name);Custom field (Lead Time Agile (Days));Custom field (Linked major incidents);Custom field (List Entity);Custom field (Loss Hypothesis);Custom field (MVP Macro Budget (K€));Custom field (Macro estimation);Custom field (Mail);Custom field (Manual Test Steps);Custom field (MoSCoW);Custom field (Next committee);Custom field (Operational categorization);Custom field (Organizations);Custom field (Original story points);Custom field (Overcoast);Custom field (PDR Symbioz);Custom field (Parent);Custom field (Parent Key);Custom field (Parent Link);Custom field (Payback);Custom field (Penalties);Custom field (Pending reason);Custom field (Period);Custom field (Phase);Custom field (Plan Types);Custom field (Platform);Custom field (Pre-Condition Type);Custom field (Pre-Conditions association with a Test);Prise en compte;Prise en compte simplified;Custom field (Probability);Custom field (Product categorization);Custom field (Program);Custom field (Project Cycle (Days));Custom field (Project Entity);Custom field (Project Owner);Custom field (QC);Custom field (Qualification Date);Custom field (Rank);Custom field (Reached (%));Custom field (Recharging);Custom field (Ref. Project CARTO);Custom field (Ref. RGP);Custom field (Ref. Symbioz);Custom field (Reference Code);Custom field (Release Date);Custom field (Request participants);Custom field (Requirement Status);Resolution Time SLA;Resolution Time SLA simplified;Custom field (Responsable Solution);Response Time SLA;Response Time SLA simplified;Custom field (Result);Custom field (Review date);Custom field (Revision);Custom field (Risk Reduction Opportunity);Satisfaction score (out of 5);Custom field (Scoring);Custom field (Script Gain Type);Custom field (Severity);Custom field (Softwares);Custom field (Source);Custom field (Specificities);Sprint;Custom field (Start Date);Custom field (Steps Count);Custom field (Story Points);Custom field (Structure Index Monitor);Custom field (Sub root cause);Custom field (Support);Custom field (T-1);Custom field (T-2);Custom field (T-Shirt Size);Custom field (T0);Custom field (T1);Custom field (T2);Custom field (T3);Custom field (T3Cl);Custom field (T4);Custom field (Target);Custom field (Target end);Custom field (Target project);Custom field (Target start);Custom field (Team);Custom field (Team List);"Temps d&#39;attribution";"Temps d&#39;attribution simplified";Temps première réponse;Temps première réponse simplified;Custom field (Test Count);Custom field (Test Environments);Custom field (Test Execution Defects);Custom field (Test Execution Status);Custom field (Test Plan);Custom field (Test Plan Status);Custom field (Test Plan Tests Filter);Custom field (Test Plans associated with a Test);Custom field (Test Repository Path);Custom field (Test Set Status);Custom field (Test Sets association with a Test);Custom field (Test Type);Custom field (TestRunStatus);Custom field (Tests associated with a Test Plan);Custom field (Tests association with a Pre-Condition);Custom field (Tests association with a Test Execution);Custom field (Tests association with a Test Set);Custom field (Time Criticality);Time to close after resolution;Time to close after resolution simplified;Time to first response;Time to first response simplified;Time to resolution;Time to resolution simplified;Custom field (TimeRecup (deprecated));Custom field (Type);Custom field (Urgency);Custom field (User Activity);Custom field (Validation Status);Custom field (Visibility List);Custom field (WSJF);Custom field (WSJF (Auto));Custom field (Workaround (deprecated));Custom field (committee);Comment
BE Geo-Redundancy;SDMTESTP-289;8724318;Test;To Do;SDMTESTP;SDM TESTPLAN;software;<EMAIL>;;;Lowest;;;<EMAIL>;<EMAIL>;06/06/2025 11:19;09/06/2025 12:11;12/06/2025 11:18;;;;COMMON;;0;;"- Normal case:
- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.\n- synchronisation outage 
- Disable the synchronisation between 2 SDM , it could be done by either or following command:
- ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK
- SET NFCFG:NFID=207,NFState=""BLOCK""
- SYNA
- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\n	- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
- enable the  synchronization between 2 SDM, by either or following command:
- SET NFCFG:NFID=107,NFState=""NORMAL""
- SET NFCFG:NFID=207,NFState=""NORMAL""
- SYNA
- check data in webagent in SDM207, the changed data could be seen.";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0.0;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SDMTESTP-289;;;;;;;;;;;;;;software;;;;;;;;;;;;;;;;;"<p><strong>When synchronization is disabled:</strong><br>
The changed data in SDM107 is <strong>not</strong> seen in SDM207.</p>
<p><strong>When synchronization is enabled:</strong><br>
The changed data in SDM107 is seen in SDM207.</p>";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0|j0qk5c:;;;;;;;;;;;;;;;;;;;;;;;;;;;;2.0;;;;;;;;;;;;;;;;;;;;;;;;;;;"{""issueId"":8724318,""testStatuses"":[]}";;;;;;;;Manual;TODO;;;;;;;;;;;;;;;;;;;;;;
EPC HSS VoLTE SRVCC;SDMTESTP-286;8700061;Test;To Do;SDMTESTP;SDM TESTPLAN;software;<EMAIL>;;;Low;;;<EMAIL>;<EMAIL>;03/06/2025 09:17;06/06/2025 17:27;01/07/2025 12:00;;;;IMS-HSS;;0;;"SRVCC and its variants are network based mechanisms by which voice call continuity can be assured from IMS voice over PS access and CS voice service when the UE is capable of transmitting/receiving on only one of those access networks at a given time. they are intermediate solutions to meet the scenarios that VoLTE services can't assure voice continuity due to insufficient LTE coverage at the beginning of LTE rollout and wide legacy CS networks' coverage can be resorted to. Since LTE and VoLTE services are a fundamental part of next-generation mobile networks, SRVCC is a key capability while LTE coverage continues to be spotty.
 

SRVCC is defined in 3GPP TS 23.216 for voice call continuity from LTEtoCS (CircuitSwitched), SRVCC allows IMS session continuity (specified in 3GPP TS 23.237) when the UE has a single radio, thus only one RAT can be active at one time.
When moving out from IMS Voice capable LTE coverage, SRVCC allows voice continuity via handover to 2G/3G CS. It is considered an important business advantage for operators since it allows a superior VoIP service that cannot be matched by third party voice application providers until LTE coverage is perfected.

As an SRVCC-capable mobile engaged in a voice call determines that it is moving away from LTE coverage, it notifies the LTE network. The LTE network determines that the voice call needs to be moved to the legacy circuit domain. It notifies the MSC server of the need to switch the voice call from the packet to the circuit domain and initiates a handover of the LTE voice bearer to the circuit network. In this step, MME shall carry a STN-SR number identifying SCCAS in IMS core and C-MSISDN (correlation MSISDN to find the original call leg in IMS), which is downloaded from EPC HSS as the user initiates attach procedure. The MSC server establishes a bearer path for the mobile in the legacy network and notifies the IMS core that the mobile's call leg is moving from the packet to the circuit domain. The circuit-packet function in the IMS core then performs the necessary interworking functions. When the mobile arrive on-channel in the legacy network, it switches its internal voice processing from VoIP to legacy-circuit voice, and the call continues.";;<EMAIL>;;;;;;;;;;;;;;;;;"06/Jun/25 1:19 PM;<EMAIL>;20250509_S6a_ULR-SRVCC_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884442/20250509_S6a_ULR-SRVCC_OK.pcapng";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0.0;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SDMTESTP-286;;;;;;;;;;;;;;software;;;;;;;;;;;;;;;;;"[{""id"":3757874,""index"":1,""fields"":{""Action"":""- User A is provisionned with STN-SR value in his profile\n- User A initiate a S6a MME registration\n- MME support the SRVCC capability\n- MME send a ULR request to the EPC-HSS"",""Data"":"""",""Expected Result"":""- The EPC-HSS reply with ULA response that contain the STN-SR provisionned""},""attachments"":[],""testVersionId"":1062556}]";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0|j0mh1s:;;;;;;;;;;;;;;;;;;;;;;;;;;;;1.0;;;;;;;;;;;;;;;;;;;;;;;;;;;"{""issueId"":8700061,""testStatuses"":[]}";;;;;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support;;;Manual;PASS;;;;;;;;;;;;;;;;;;;;;;
HLR Any Time Interrogation - CS;SDMTESTP-276;8697580;Test;To Do;SDMTESTP;SDM TESTPLAN;software;<EMAIL>;;;Low;;;<EMAIL>;<EMAIL>;02/06/2025 16:19;06/06/2025 17:41;01/07/2025 12:00;;;;HLR;;0;;With this procedure a network node can acquire the subscriber current location information and state information from the HLR;;<EMAIL>;;;;;;;;;;;;;;;;;"06/Jun/25 1:16 PM;<EMAIL>;20250515_MAP_ATI_CS_OK.pcapng;https://portail.agir.orange.com/secure/attachment/3884436/20250515_MAP_ATI_CS_OK.pcapng";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0.0;;;;;;"{""params"":[]}";"{""params"":[]}";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SANTSDM-18;;;;;;;;;;;;;;software;;;;;;;;;;;;;;;;;"[{""id"":3757257,""index"":1,""fields"":{""Action"":""|- Subscriber A is registered under VLR\n- A network node (gsmSCF) send a ATI request for CS information retreival from the HLR|"",""Data"":"""",""Expected Result"":""- On receipt of ATI request, the HLR initiate an PSI request to the VLR\n- On receipt of PSI response the HLR return the ATI response with CS information requested""},""attachments"":[],""testVersionId"":1062322}]";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0|j0m228:;;;;;;;;;;;;;;;;;;;;;;;;;;;;1.0;;;;;;;;;;;;;;;;;;;;;;;;;;;"{""issueId"":8697580,""testStatuses"":[]}";;;;;/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management;;;Manual;PASS;;;;;;;;;;;;;;;;;;;;;;
