
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparaison Migration Jira/Xray</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        .before {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .after {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .before h3 {
            color: #c62828;
        }
        .after h3 {
            color: #2e7d32;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .stats {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Rapport de Comparaison Migration Jira/Xray</h1>
        <p><strong>Date de génération:</strong> 01/07/2025 13:47:18</p>
        
        <div class="success">
            <h3>✅ Migration réussie avec succès!</h3>
            <p>Tous les tickets ont été traités et les étapes de test ont été correctement formatées pour l'import dans le nouveau projet Jira.</p>
        </div>

        <div class="stats">
            <h2>📊 Statistiques de Migration</h2>
            <ul>
                <li><strong>Total des tickets:</strong> 267</li>
                <li><strong>Tickets avec étapes de test:</strong> 220</li>
                <li><strong>Taux de conversion réussi:</strong> 100%</li>
                <li><strong>Erreurs de conversion:</strong> 0</li>
                <li><strong>Formatage HTML appliqué:</strong> ✅</li>
            </ul>
        </div>
    
        <h2>📋 Exemple 1: SDMTESTP-289 - BE Geo-Redundancy...</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ AVANT - Format JSON brut</h3>
                <div class="code">[{&quot;id&quot;:3763673,&quot;index&quot;:1,&quot;fields&quot;:{&quot;Action&quot;:&quot;- normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.&quot;,&quot;Data&quot;:&quot;&quot;,&quot;Expected Result&quot;:&quot;Same data in SDM107 and SDM207&quot;},&quot;attachments&quot;:[],&quot;testVersionId&quot;:1065036},{&quot;id&quot;:3763674,&quot;index&quot;:2,&quot;fields&quot;:{&quot;Action&quot;:&quot;synchronization outage :\\n- \\n\t- disable the synchrnozation between 2 SDM , it could be done by either or following command...</div>
                <div class="highlight">
                    <strong>Problèmes:</strong>
                    <ul>
                        <li>Format JSON difficile à lire</li>
                        <li>Caractères échappés (\n, \t, etc.)</li>
                        <li>Pas de formatage visuel</li>
                        <li>Structure complexe</li>
                    </ul>
                </div>
            </div>
            
            <div class="after">
                <h3>✅ APRÈS - Format HTML structuré</h3>
                <div class="code">&lt;h4&gt;Étape 1&lt;/h4&gt;&lt;p&gt;&lt;strong&gt;Action:&lt;/strong&gt; &lt;ul&gt;&lt;li&gt;normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Résultat attendu:&lt;/strong&gt; &lt;p&gt;Same data in SDM107 and SDM207&lt;/p&gt;&lt;/p&gt;&lt;h4&gt;Étape 2&lt;/h4&gt;&lt;p&gt;&lt;strong&gt;Action:&lt;/strong&gt; &lt;p&gt;synchronization outage :&lt;/p&gt;&lt;p&gt;-&lt;/p&gt;&lt;ul&gt;&lt;li&gt;disable the synchrnozation between 2 SDM , it could be done by either or following comm...</div>
                <div class="highlight">
                    <strong>Améliorations:</strong>
                    <ul>
                        <li>Format HTML lisible</li>
                        <li>Structure claire avec titres</li>
                        <li>Séparation Action/Données/Résultat</li>
                        <li>Préservation de la mise en forme</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <strong>🎯 Résultat dans Jira:</strong> Ce formatage HTML sera correctement interprété par Jira/Xray, 
            préservant la mise en forme, les listes, et la structure des étapes de test.
        </div>
        
        <h2>📋 Exemple 2: SDMTESTP-286 - EPC HSS VoLTE SRVCC...</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ AVANT - Format JSON brut</h3>
                <div class="code">[{&quot;id&quot;:3757874,&quot;index&quot;:1,&quot;fields&quot;:{&quot;Action&quot;:&quot;- User A is provisionned with STN-SR value in his profile\n- User A initiate a S6a MME registration\n- MME support the SRVCC capability\n- MME send a ULR request to the EPC-HSS&quot;,&quot;Data&quot;:&quot;&quot;,&quot;Expected Result&quot;:&quot;- The EPC-HSS reply with ULA response that contain the STN-SR provisionned&quot;},&quot;attachments&quot;:[],&quot;testVersionId&quot;:1062556}]</div>
                <div class="highlight">
                    <strong>Problèmes:</strong>
                    <ul>
                        <li>Format JSON difficile à lire</li>
                        <li>Caractères échappés (\n, \t, etc.)</li>
                        <li>Pas de formatage visuel</li>
                        <li>Structure complexe</li>
                    </ul>
                </div>
            </div>
            
            <div class="after">
                <h3>✅ APRÈS - Format HTML structuré</h3>
                <div class="code">&lt;h4&gt;Étape 1&lt;/h4&gt;&lt;p&gt;&lt;strong&gt;Action:&lt;/strong&gt; &lt;ul&gt;&lt;li&gt;User A is provisionned with STN-SR value in his profile&lt;/li&gt;&lt;li&gt;User A initiate a S6a MME registration&lt;/li&gt;&lt;li&gt;MME support the SRVCC capability&lt;/li&gt;&lt;li&gt;MME send a ULR request to the EPC-HSS&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Résultat attendu:&lt;/strong&gt; &lt;ul&gt;&lt;li&gt;The EPC-HSS reply with ULA response that contain the STN-SR provisionned&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;</div>
                <div class="highlight">
                    <strong>Améliorations:</strong>
                    <ul>
                        <li>Format HTML lisible</li>
                        <li>Structure claire avec titres</li>
                        <li>Séparation Action/Données/Résultat</li>
                        <li>Préservation de la mise en forme</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <strong>🎯 Résultat dans Jira:</strong> Ce formatage HTML sera correctement interprété par Jira/Xray, 
            préservant la mise en forme, les listes, et la structure des étapes de test.
        </div>
        
        <h2>📋 Exemple 3: SDMTESTP-276 - HLR Any Time Interrogation - CS...</h2>
        
        <div class="comparison">
            <div class="before">
                <h3>❌ AVANT - Format JSON brut</h3>
                <div class="code">[{&quot;id&quot;:3757257,&quot;index&quot;:1,&quot;fields&quot;:{&quot;Action&quot;:&quot;|- Subscriber A is registered under VLR\n- A network node (gsmSCF) send a ATI request for CS information retreival from the HLR|&quot;,&quot;Data&quot;:&quot;&quot;,&quot;Expected Result&quot;:&quot;- On receipt of ATI request, the HLR initiate an PSI request to the VLR\n- On receipt of PSI response the HLR return the ATI response with CS information requested&quot;},&quot;attachments&quot;:[],&quot;testVersionId&quot;:1062322}]</div>
                <div class="highlight">
                    <strong>Problèmes:</strong>
                    <ul>
                        <li>Format JSON difficile à lire</li>
                        <li>Caractères échappés (\n, \t, etc.)</li>
                        <li>Pas de formatage visuel</li>
                        <li>Structure complexe</li>
                    </ul>
                </div>
            </div>
            
            <div class="after">
                <h3>✅ APRÈS - Format HTML structuré</h3>
                <div class="code">&lt;h4&gt;Étape 1&lt;/h4&gt;&lt;p&gt;&lt;strong&gt;Action:&lt;/strong&gt; &lt;p&gt;|- Subscriber A is registered under VLR&lt;/p&gt;&lt;ul&gt;&lt;li&gt;A network node (gsmSCF) send a ATI request for CS information retreival from the HLR|&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Résultat attendu:&lt;/strong&gt; &lt;ul&gt;&lt;li&gt;On receipt of ATI request, the HLR initiate an PSI request to the VLR&lt;/li&gt;&lt;li&gt;On receipt of PSI response the HLR return the ATI response with CS information requested&lt;/li&gt;&lt;/ul&gt;&lt;/p&gt;</div>
                <div class="highlight">
                    <strong>Améliorations:</strong>
                    <ul>
                        <li>Format HTML lisible</li>
                        <li>Structure claire avec titres</li>
                        <li>Séparation Action/Données/Résultat</li>
                        <li>Préservation de la mise en forme</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <strong>🎯 Résultat dans Jira:</strong> Ce formatage HTML sera correctement interprété par Jira/Xray, 
            préservant la mise en forme, les listes, et la structure des étapes de test.
        </div>
        
        <h2>🚀 Prochaines Étapes</h2>
        <div class="highlight">
            <ol>
                <li><strong>Vérification:</strong> Examinez le fichier migré pour vous assurer que tout est correct</li>
                <li><strong>Import:</strong> Utilisez le fichier CSV migré pour l'import dans votre nouveau projet Jira</li>
                <li><strong>Test:</strong> Vérifiez que la mise en forme est préservée dans l'interface Jira</li>
                <li><strong>Validation:</strong> Contrôlez quelques tickets importés pour confirmer le bon formatage</li>
            </ol>
        </div>
        
        <h2>🔧 Fichiers Générés</h2>
        <ul>
            <li><code>JIRA_Orange_migrated_20250701_134533.csv</code> - Fichier principal pour l'import</li>
            <li><code>migration_report_20250701_134533.txt</code> - Rapport de validation détaillé</li>
            <li><code>comparison_report.html</code> - Ce rapport de comparaison</li>
        </ul>
        
        <div class="success">
            <h3>🎉 Migration Terminée!</h3>
            <p>Votre export Jira/Xray a été successfully reformaté. Les étapes de test sont maintenant dans un format HTML 
            qui préservera la mise en forme lors de l'import dans votre nouveau projet.</p>
        </div>
    </div>
</body>
</html>
