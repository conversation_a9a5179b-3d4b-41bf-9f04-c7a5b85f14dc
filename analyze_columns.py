#!/usr/bin/env python3
"""
Analyser quelles colonnes contiennent des données dans le fichier CSV
"""

import csv

def analyze_columns():
    """Analyse les colonnes pour voir lesquelles contiennent des données"""
    
    filename = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
    
    print("🔍 ANALYSE DES COLONNES UTILISÉES")
    print("=" * 50)
    
    # Colonnes d'intérêt spécifiques
    target_columns = ['Description', 'Priority', 'Component/s', 'Labels']
    
    column_stats = {}
    
    with open(filename, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        # Initialiser les stats pour toutes les colonnes
        for col in reader.fieldnames:
            column_stats[col] = {'non_empty': 0, 'total': 0, 'examples': []}
        
        # Analyser chaque ligne
        for row in reader:
            for col, value in row.items():
                column_stats[col]['total'] += 1
                if value and value.strip():
                    column_stats[col]['non_empty'] += 1
                    # Garder quelques exemples
                    if len(column_stats[col]['examples']) < 3:
                        column_stats[col]['examples'].append(value[:50] + '...' if len(value) > 50 else value)
    
    print(f"\n📊 COLONNES D'INTÉRÊT DEMANDÉES:")
    print("-" * 40)
    
    for col in target_columns:
        if col in column_stats:
            stats = column_stats[col]
            percentage = (stats['non_empty'] / stats['total']) * 100 if stats['total'] > 0 else 0
            status = "✅ UTILISÉE" if stats['non_empty'] > 0 else "❌ VIDE"
            
            print(f"\n🔸 {col}:")
            print(f"   Status: {status}")
            print(f"   Remplissage: {stats['non_empty']}/{stats['total']} ({percentage:.1f}%)")
            
            if stats['examples']:
                print(f"   Exemples: {', '.join(stats['examples'])}")
        else:
            print(f"\n🔸 {col}: ❌ COLONNE NON TROUVÉE")
    
    print(f"\n📋 AUTRES COLONNES IMPORTANTES AVEC DONNÉES:")
    print("-" * 45)
    
    # Montrer les colonnes importantes qui ont des données
    important_cols = ['Summary', 'Issue key', 'Issue Type', 'Status', 'Priority', 'Description', 
                     'Component/s', 'Labels', 'Created', 'Updated', 'Reporter', 'Assignee']
    
    for col in important_cols:
        if col in column_stats:
            stats = column_stats[col]
            if stats['non_empty'] > 0:
                percentage = (stats['non_empty'] / stats['total']) * 100
                print(f"   ✅ {col}: {stats['non_empty']}/{stats['total']} ({percentage:.1f}%)")

if __name__ == '__main__':
    analyze_columns()
