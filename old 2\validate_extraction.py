#!/usr/bin/env python3
"""
Script de validation de l'extraction des étapes de test
Génère un rapport détaillé de comparaison avant/après
"""

import csv
import json
from typing import Dict, List, Any

def validate_extraction():
    """Valide l'extraction et génère un rapport"""
    
    original_file = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
    extracted_file = "JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv"
    
    print("🔍 VALIDATION DE L'EXTRACTION DES ÉTAPES DE TEST")
    print("=" * 60)
    
    # Analyser le fichier original
    original_stats = analyze_original_file(original_file)
    
    # Analyser le fichier extrait
    extracted_stats = analyze_extracted_file(extracted_file)
    
    # Générer le rapport
    generate_report(original_stats, extracted_stats)
    
    # Créer des exemples de comparaison
    create_comparison_examples(original_file, extracted_file)

def analyze_original_file(filename: str) -> Dict[str, Any]:
    """Analyse le fichier original"""
    
    print(f"\n📊 Analyse du fichier original: {filename}")
    
    stats = {
        'total_tickets': 0,
        'tickets_with_steps': 0,
        'total_steps': 0,
        'examples': []
    }
    
    with open(filename, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            stats['total_tickets'] += 1
            
            steps_json = row.get('Custom field (Manual Test Steps)', '')
            if steps_json and steps_json != '[]':
                try:
                    steps_data = json.loads(steps_json)
                    if steps_data:
                        stats['tickets_with_steps'] += 1
                        stats['total_steps'] += len(steps_data)
                        
                        # Garder quelques exemples
                        if len(stats['examples']) < 3:
                            stats['examples'].append({
                                'ticket': row.get('Issue key', 'N/A'),
                                'summary': row.get('Summary', 'N/A'),
                                'steps_count': len(steps_data),
                                'steps_json': steps_json[:200] + '...' if len(steps_json) > 200 else steps_json
                            })
                except json.JSONDecodeError:
                    continue
    
    print(f"   ✅ Total tickets: {stats['total_tickets']}")
    print(f"   ✅ Tickets avec étapes: {stats['tickets_with_steps']}")
    print(f"   ✅ Total étapes JSON: {stats['total_steps']}")
    
    return stats

def analyze_extracted_file(filename: str) -> Dict[str, Any]:
    """Analyse le fichier extrait"""
    
    print(f"\n📊 Analyse du fichier extrait: {filename}")
    
    stats = {
        'total_rows': 0,
        'rows_with_action': 0,
        'rows_with_data': 0,
        'rows_with_expected': 0,
        'examples': []
    }
    
    with open(filename, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            stats['total_rows'] += 1
            
            action = row.get('Action', '').strip()
            data = row.get('Data', '').strip()
            expected = row.get('Expected Result', '').strip()
            
            if action:
                stats['rows_with_action'] += 1
            if data:
                stats['rows_with_data'] += 1
            if expected:
                stats['rows_with_expected'] += 1
            
            # Garder quelques exemples
            if len(stats['examples']) < 3 and action:
                stats['examples'].append({
                    'ticket': row.get('Issue key', 'N/A'),
                    'summary': row.get('Summary', 'N/A')[:50] + '...',
                    'action': action[:100] + '...' if len(action) > 100 else action,
                    'data': data[:50] + '...' if len(data) > 50 else data,
                    'expected': expected[:100] + '...' if len(expected) > 100 else expected
                })
    
    print(f"   ✅ Total lignes: {stats['total_rows']}")
    print(f"   ✅ Lignes avec Action: {stats['rows_with_action']}")
    print(f"   ✅ Lignes avec Data: {stats['rows_with_data']}")
    print(f"   ✅ Lignes avec Expected Result: {stats['rows_with_expected']}")
    
    return stats

def generate_report(original_stats: Dict, extracted_stats: Dict):
    """Génère le rapport de validation"""
    
    print("\n📋 RAPPORT DE VALIDATION")
    print("=" * 40)
    
    print(f"\n🔢 STATISTIQUES GÉNÉRALES:")
    print(f"   • Tickets originaux: {original_stats['total_tickets']}")
    print(f"   • Lignes extraites: {extracted_stats['total_rows']}")
    print(f"   • Tickets avec étapes JSON: {original_stats['tickets_with_steps']}")
    print(f"   • Lignes avec Action extraite: {extracted_stats['rows_with_action']}")
    
    print(f"\n✅ VALIDATION:")
    if extracted_stats['rows_with_action'] >= original_stats['total_steps']:
        print(f"   ✅ Extraction réussie: {extracted_stats['rows_with_action']} actions extraites")
    else:
        print(f"   ⚠️  Possible perte: {original_stats['total_steps']} étapes JSON vs {extracted_stats['rows_with_action']} actions")
    
    print(f"\n📊 RÉPARTITION DES DONNÉES:")
    print(f"   • Actions non vides: {extracted_stats['rows_with_action']}")
    print(f"   • Data non vides: {extracted_stats['rows_with_data']}")
    print(f"   • Expected Results non vides: {extracted_stats['rows_with_expected']}")
    
    # Calcul du taux de remplissage
    if extracted_stats['rows_with_action'] > 0:
        data_rate = (extracted_stats['rows_with_data'] / extracted_stats['rows_with_action']) * 100
        expected_rate = (extracted_stats['rows_with_expected'] / extracted_stats['rows_with_action']) * 100
        
        print(f"\n📈 TAUX DE REMPLISSAGE:")
        print(f"   • Data: {data_rate:.1f}%")
        print(f"   • Expected Result: {expected_rate:.1f}%")

def create_comparison_examples(original_file: str, extracted_file: str):
    """Crée des exemples de comparaison"""
    
    print(f"\n🔍 EXEMPLES DE COMPARAISON")
    print("=" * 40)
    
    # Lire les fichiers pour comparaison
    original_data = {}
    with open(original_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            key = row.get('Issue key', '')
            if key:
                original_data[key] = row
    
    extracted_examples = []
    with open(extracted_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            action = row.get('Action', '').strip()
            if action and len(extracted_examples) < 2:  # Limiter à 2 exemples
                extracted_examples.append(row)
    
    for i, example in enumerate(extracted_examples, 1):
        ticket_key = example.get('Issue key', '')
        original_row = original_data.get(ticket_key, {})
        
        print(f"\n📋 EXEMPLE {i}: {ticket_key}")
        print(f"   Summary: {example.get('Summary', 'N/A')[:60]}...")
        
        print(f"\n   🔴 AVANT (JSON):")
        original_steps = original_row.get('Custom field (Manual Test Steps)', '')
        if original_steps:
            print(f"   {original_steps[:150]}...")
        
        print(f"\n   🟢 APRÈS (Colonnes séparées):")
        print(f"   Action: {example.get('Action', '')[:100]}...")
        print(f"   Data: {example.get('Data', '') or '(vide)'}")
        print(f"   Expected: {example.get('Expected Result', '')[:100]}...")

def main():
    """Point d'entrée principal"""
    try:
        validate_extraction()
        
        print(f"\n🎉 VALIDATION TERMINÉE!")
        print(f"\n📁 FICHIERS GÉNÉRÉS:")
        print(f"   • JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv")
        print(f"\n🚀 PROCHAINES ÉTAPES:")
        print(f"   1. Vérifiez le fichier extrait")
        print(f"   2. Importez-le dans votre nouveau projet Jira/Xray")
        print(f"   3. Les colonnes Action, Data, Expected Result sont maintenant séparées")
        print(f"   4. Chaque étape de test est sur une ligne distincte")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la validation: {e}")

if __name__ == '__main__':
    main()
