#!/usr/bin/env python3
"""
Script de validation pour la migration Jira/Xray
Vérifie que les données ont été correctement formatées
"""

import csv
import json
import argparse
import os
from collections import defaultdict
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MigrationValidator:
    def __init__(self, original_file: str, migrated_file: str):
        self.original_file = original_file
        self.migrated_file = migrated_file
        self.validation_report = defaultdict(int)
    
    def validate_files_exist(self) -> bool:
        """Vérifie que les fichiers existent"""
        if not os.path.exists(self.original_file):
            logger.error(f"Fichier original non trouvé: {self.original_file}")
            return False
        
        if not os.path.exists(self.migrated_file):
            logger.error(f"Fichier migré non trouvé: {self.migrated_file}")
            return False
        
        return True
    
    def count_records(self) -> tuple:
        """Compte les enregistrements dans les deux fichiers"""
        try:
            with open(self.original_file, 'r', encoding='utf-8') as f:
                original_count = sum(1 for line in csv.DictReader(f))
            
            with open(self.migrated_file, 'r', encoding='utf-8') as f:
                migrated_count = sum(1 for line in csv.DictReader(f))
            
            return original_count, migrated_count
        
        except Exception as e:
            logger.error(f"Erreur lors du comptage des enregistrements: {e}")
            return 0, 0
    
    def validate_test_steps_conversion(self) -> dict:
        """Valide la conversion des étapes de test"""
        validation_stats = {
            'total_tickets': 0,
            'tickets_with_steps': 0,
            'tickets_steps_converted': 0,
            'conversion_errors': 0,
            'empty_steps_after_conversion': 0
        }
        
        try:
            with open(self.original_file, 'r', encoding='utf-8') as orig_f, \
                 open(self.migrated_file, 'r', encoding='utf-8') as migr_f:
                
                orig_reader = csv.DictReader(orig_f)
                migr_reader = csv.DictReader(migr_f)
                
                # Convertir en listes pour pouvoir les parcourir ensemble
                orig_rows = list(orig_reader)
                migr_rows = list(migr_reader)
                
                if len(orig_rows) != len(migr_rows):
                    logger.warning(f"Nombre de lignes différent: {len(orig_rows)} vs {len(migr_rows)}")
                
                for i, (orig_row, migr_row) in enumerate(zip(orig_rows, migr_rows)):
                    validation_stats['total_tickets'] += 1
                    
                    # Vérifier les étapes de test originales
                    orig_steps = orig_row.get('Custom field (Manual Test Steps)', '')
                    migr_steps = migr_row.get('Manual Test Steps (Formatted)', '')
                    
                    if orig_steps and orig_steps != '[]':
                        validation_stats['tickets_with_steps'] += 1
                        
                        try:
                            # Parser les étapes originales
                            steps_data = json.loads(orig_steps)
                            if steps_data:
                                if migr_steps:
                                    validation_stats['tickets_steps_converted'] += 1
                                else:
                                    validation_stats['empty_steps_after_conversion'] += 1
                                    logger.warning(f"Ligne {i+1}: Étapes vides après conversion")
                        
                        except json.JSONDecodeError:
                            validation_stats['conversion_errors'] += 1
                            logger.warning(f"Ligne {i+1}: Erreur de parsing JSON des étapes originales")
        
        except Exception as e:
            logger.error(f"Erreur lors de la validation des étapes de test: {e}")
        
        return validation_stats
    
    def check_html_formatting(self) -> dict:
        """Vérifie que le formatage HTML est correct"""
        html_stats = {
            'tickets_with_html': 0,
            'malformed_html': 0,
            'common_html_tags': defaultdict(int)
        }
        
        html_tags = ['<p>', '<ul>', '<ol>', '<li>', '<h1>', '<h2>', '<h3>', '<h4>', '<h5>', '<h6>', 
                     '<strong>', '<em>', '<code>', '<br>']
        
        try:
            with open(self.migrated_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    formatted_steps = row.get('Manual Test Steps (Formatted)', '')
                    description = row.get('Description', '')
                    
                    # Vérifier les étapes formatées
                    if formatted_steps:
                        has_html = any(tag in formatted_steps for tag in html_tags)
                        if has_html:
                            html_stats['tickets_with_html'] += 1
                            
                            # Compter les tags utilisés
                            for tag in html_tags:
                                count = formatted_steps.count(tag)
                                if count > 0:
                                    html_stats['common_html_tags'][tag] += count
                            
                            # Vérifier les balises mal formées (basique)
                            if '<' in formatted_steps and '>' in formatted_steps:
                                # Vérification simple des balises fermées
                                for tag in ['<ul>', '<ol>', '<p>']:
                                    open_count = formatted_steps.count(tag)
                                    close_tag = tag.replace('<', '</')
                                    close_count = formatted_steps.count(close_tag)
                                    if open_count != close_count:
                                        html_stats['malformed_html'] += 1
                                        break
        
        except Exception as e:
            logger.error(f"Erreur lors de la vérification du formatage HTML: {e}")
        
        return html_stats
    
    def generate_report(self) -> str:
        """Génère un rapport de validation complet"""
        if not self.validate_files_exist():
            return "❌ Validation échouée: fichiers manquants"
        
        # Compter les enregistrements
        orig_count, migr_count = self.count_records()
        
        # Valider la conversion des étapes
        steps_stats = self.validate_test_steps_conversion()
        
        # Vérifier le formatage HTML
        html_stats = self.check_html_formatting()
        
        # Générer le rapport
        report = []
        report.append("=" * 60)
        report.append("RAPPORT DE VALIDATION DE MIGRATION JIRA/XRAY")
        report.append("=" * 60)
        report.append("")
        
        # Statistiques générales
        report.append("📊 STATISTIQUES GÉNÉRALES")
        report.append("-" * 30)
        report.append(f"Tickets dans le fichier original: {orig_count}")
        report.append(f"Tickets dans le fichier migré: {migr_count}")
        
        if orig_count == migr_count:
            report.append("✅ Nombre de tickets conservé")
        else:
            report.append("⚠️  Nombre de tickets différent")
        report.append("")
        
        # Statistiques des étapes de test
        report.append("🧪 CONVERSION DES ÉTAPES DE TEST")
        report.append("-" * 35)
        report.append(f"Total des tickets: {steps_stats['total_tickets']}")
        report.append(f"Tickets avec étapes: {steps_stats['tickets_with_steps']}")
        report.append(f"Étapes converties avec succès: {steps_stats['tickets_steps_converted']}")
        report.append(f"Erreurs de conversion: {steps_stats['conversion_errors']}")
        report.append(f"Étapes vides après conversion: {steps_stats['empty_steps_after_conversion']}")
        
        if steps_stats['tickets_with_steps'] > 0:
            success_rate = (steps_stats['tickets_steps_converted'] / steps_stats['tickets_with_steps']) * 100
            report.append(f"Taux de réussite: {success_rate:.1f}%")
        report.append("")
        
        # Statistiques HTML
        report.append("🎨 FORMATAGE HTML")
        report.append("-" * 20)
        report.append(f"Tickets avec formatage HTML: {html_stats['tickets_with_html']}")
        report.append(f"Tickets avec HTML mal formé: {html_stats['malformed_html']}")
        
        if html_stats['common_html_tags']:
            report.append("\nTags HTML les plus utilisés:")
            for tag, count in sorted(html_stats['common_html_tags'].items(), 
                                   key=lambda x: x[1], reverse=True)[:5]:
                report.append(f"  {tag}: {count} occurrences")
        report.append("")
        
        # Recommandations
        report.append("💡 RECOMMANDATIONS")
        report.append("-" * 20)
        
        if steps_stats['conversion_errors'] > 0:
            report.append("⚠️  Vérifier les tickets avec erreurs de conversion")
        
        if html_stats['malformed_html'] > 0:
            report.append("⚠️  Corriger les balises HTML mal formées")
        
        if steps_stats['empty_steps_after_conversion'] > 0:
            report.append("⚠️  Examiner les tickets avec étapes vides après conversion")
        
        if (steps_stats['conversion_errors'] == 0 and 
            html_stats['malformed_html'] == 0 and 
            steps_stats['empty_steps_after_conversion'] == 0):
            report.append("✅ Migration réussie sans problèmes détectés")
        
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description='Validation de migration Jira/Xray')
    parser.add_argument('original_file', help='Fichier CSV original')
    parser.add_argument('migrated_file', help='Fichier CSV migré')
    parser.add_argument('-o', '--output', help='Fichier de rapport (optionnel)')
    
    args = parser.parse_args()
    
    validator = MigrationValidator(args.original_file, args.migrated_file)
    report = validator.generate_report()
    
    print(report)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"Rapport sauvegardé dans {args.output}")

if __name__ == '__main__':
    main()
