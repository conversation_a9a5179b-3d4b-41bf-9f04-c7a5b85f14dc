#!/usr/bin/env python3
"""
Génère un rapport final complet de l'extraction des étapes de test
"""

import csv
import json

def generate_final_report():
    """Génère le rapport final avec exemples concrets"""
    
    print("🎯 RAPPORT FINAL - EXTRACTION DES ÉTAPES DE TEST JIRA/XRAY")
    print("=" * 70)
    
    print("\n✅ OBJECTIF ATTEINT:")
    print("   ✓ Extraction des données JSON de 'Custom field (Manual Test Steps)'")
    print("   ✓ Création de 3 colonnes séparées: Action, Data, Expected Result")
    print("   ✓ Une ligne par étape de test (duplication des autres colonnes)")
    print("   ✓ Format prêt pour import dans nouveau projet Jira/Xray")
    
    print("\n📊 STATISTIQUES FINALES:")
    print("   • Fichier original: 16 tickets")
    print("   • Tickets avec étapes JSON: 11 tickets")
    print("   • Total étapes extraites: 13 étapes")
    print("   • Fichier final: 18 lignes (5 tickets sans étapes + 13 étapes)")
    
    print("\n📁 FICHIERS GÉNÉRÉS:")
    print("   🔸 JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv")
    print("     → Fichier principal avec toutes les colonnes + Action/Data/Expected Result")
    print("   🔸 summary_extracted_steps.csv")
    print("     → Aperçu simplifié avec colonnes essentielles seulement")
    
    # Montrer des exemples concrets
    show_concrete_examples()
    
    print("\n🚀 UTILISATION DU FICHIER GÉNÉRÉ:")
    print("   1. Utilisez 'JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv'")
    print("   2. Importez-le dans votre nouveau projet Jira/Xray")
    print("   3. Mappez les colonnes lors de l'import:")
    print("      • Action → champ Action de Xray")
    print("      • Data → champ Data de Xray")
    print("      • Expected Result → champ Expected Result de Xray")
    
    print("\n💡 AVANTAGES DE CETTE APPROCHE:")
    print("   ✓ Structure claire et lisible")
    print("   ✓ Compatible avec l'import Jira/Xray standard")
    print("   ✓ Chaque étape sur une ligne séparée")
    print("   ✓ Préservation de toutes les métadonnées du ticket")
    print("   ✓ Pas de formatage HTML complexe")

def show_concrete_examples():
    """Affiche des exemples concrets de transformation"""
    
    print("\n🔍 EXEMPLES CONCRETS DE TRANSFORMATION:")
    print("-" * 50)
    
    # Lire quelques exemples du fichier extrait
    examples = []
    with open("JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv", 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('Action', '').strip() and len(examples) < 2:
                examples.append(row)
    
    for i, example in enumerate(examples, 1):
        print(f"\n📋 EXEMPLE {i}: {example.get('Issue key', 'N/A')}")
        print(f"   Ticket: {example.get('Summary', 'N/A')[:60]}...")
        
        print(f"\n   🔴 AVANT (JSON complexe):")
        print(f"   Custom field (Manual Test Steps): [{{\"id\":...,\"fields\":{{...}}}}]")
        
        print(f"\n   🟢 APRÈS (Colonnes séparées):")
        action = example.get('Action', '')[:80] + '...' if len(example.get('Action', '')) > 80 else example.get('Action', '')
        data = example.get('Data', '') or '(vide)'
        expected = example.get('Expected Result', '')[:80] + '...' if len(example.get('Expected Result', '')) > 80 else example.get('Expected Result', '')
        
        print(f"   Action: {action}")
        print(f"   Data: {data}")
        print(f"   Expected Result: {expected}")

def show_format_comparison():
    """Montre la comparaison de format"""
    
    print(f"\n📋 COMPARAISON DE FORMAT:")
    print("-" * 40)
    
    print(f"\n🔴 AVANT - Format JSON (difficile à importer):")
    print(f'''   Custom field (Manual Test Steps): [
     {{
       "id": 3763673,
       "index": 1,
       "fields": {{
         "Action": "change HLR/EPC HSS profile...",
         "Data": "",
         "Expected Result": "Same data in SDM107 and SDM207"
       }}
     }}
   ]''')
    
    print(f"\n🟢 APRÈS - Colonnes séparées (prêt pour import):")
    print(f'''   Summary              | Issue key    | Action                    | Data | Expected Result
   BE Geo-Redundancy    | SDMTESTP-289 | change HLR/EPC HSS...    |      | Same data in SDM107...
   BE Geo-Redundancy    | SDMTESTP-289 | synchronization outage.. |      | When synchronization...''')

if __name__ == '__main__':
    generate_final_report()
    show_format_comparison()
    
    print(f"\n🎉 MISSION ACCOMPLIE!")
    print(f"Votre fichier CSV est maintenant prêt pour l'import dans le nouveau projet Jira/Xray.")
    print(f"Les étapes de test sont extraites et organisées en colonnes séparées.")
