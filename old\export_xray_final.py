import csv

input_file = 'tickets_xray_import_ready.csv'
output_file = 'tickets_xray_import_final.csv'

with open(input_file, newline='', encoding='utf-8') as csv_in, \
     open(output_file, 'w', newline='', encoding='utf-8') as csv_out:

    reader = csv.DictReader(csv_in, delimiter=';')
    fieldnames = [fn for fn in reader.fieldnames if fn not in ['Step Action', 'Step Data', 'Step Expected Result']]
    fieldnames.append('Steps')  # nouvelle colonne Steps

    writer = csv.DictWriter(csv_out, fieldnames=fieldnames, delimiter=';')
    writer.writeheader()

    for row in reader:
        # Supprimer les clés inattendues
        if None in row:
            del row[None]

        steps_text = ''
        action = row.get('Step Action', '').strip()
        data = row.get('Step Data', '').strip()
        expected = row.get('Step Expected Result', '').strip()
        if action or data or expected:
            steps_text = f"{action} ||| {data} ||| {expected}"
        row.pop('Step Action', None)
        row.pop('Step Data', None)
        row.pop('Step Expected Result', None)
        row['Steps'] = steps_text
        writer.writerow(row)