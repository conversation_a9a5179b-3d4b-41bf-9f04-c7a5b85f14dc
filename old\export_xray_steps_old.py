import csv
import json

input_file = 'tickets_xray_export.csv'
output_file = 'tickets_xray_import_ready.csv'

with open(input_file, newline='', encoding='utf-8') as csv_in, \
     open(output_file, 'w', newline='', encoding='utf-8') as csv_out:

    reader = csv.DictReader(csv_in, delimiter=';')
    fieldnames = [name for name in reader.fieldnames if name != 'Custom field (Manual Test Steps)']
    fieldnames += ['Step Action', 'Step Data', 'Step Expected Result']

    writer = csv.DictWriter(csv_out, fieldnames=fieldnames, delimiter=';', quoting=csv.QUOTE_ALL)
    writer.writeheader()

    for row in reader:
        steps_json = row.get('Custom field (Manual Test Steps)', '[]')
        try:
            steps = json.loads(steps_json)
        except json.JSONDecodeError:
            steps = []

        if steps:
            for step in steps:
                new_row = row.copy()
                new_row.pop('Custom field (Manual Test Steps)', None)
                new_row['Step Action'] = step['fields']['Action']
                new_row['Step Data'] = step['fields']['Data']
                new_row['Step Expected Result'] = step['fields']['Expected Result']
                writer.writerow(new_row)
        else:
            new_row = row.copy()
            new_row.pop('Custom field (Manual Test Steps)', None)
            new_row['Step Action'] = ''
            new_row['Step Data'] = ''
            new_row['Step Expected Result'] = ''
            writer.writerow(new_row)
