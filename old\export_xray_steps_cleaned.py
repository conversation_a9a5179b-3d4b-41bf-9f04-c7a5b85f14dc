import csv
import re

input_file = 'tickets_xray_import_ready.csv'
output_file = 'tickets_xray_import_ready_fixed.csv'

def clean_and_format_text(text):
    """
    Nettoie les caractères parasites et transforme le Markdown-like en HTML (ul/ol/li)
    pour que Xray/Jira affiche correctement les indentations et les titres.
    """
    if not text:
        return ''

    # Remplacer les caractères parasites et encodages problématiques
    text = text.replace('Â ', ' ')
    text = text.replace('Â', '')
    text = text.replace('ï¼Œ', ',')
    text = text.replace('\\n', '\n')  # Convertir les \n littéraux en vraies nouvelles lignes
    text = text.replace('\\t', '\t')  # Convertir les \t littéraux en vraies tabulations
    text = text.replace('\u003d', '=')  # Décoder les caractères Unicode échappés
    text = text.replace('""', '"')  # Nettoyer les guillemets doublés
    text = text.replace('###', '### ')  # pour être sûr que les titres sont bien espacés

    # Découper en lignes
    lines = text.splitlines()
    formatted_lines = []
    in_list = False
    in_ordered_list = False

    for line in lines:
        stripped = line.strip()

        # Titres Markdown -> <h1>, <h2>, <h3> etc.
        header_match = re.match(r'^(#{1,6})\s*(.*)', stripped)
        if header_match:
            level = len(header_match.group(1))
            content = header_match.group(2).strip()
            if in_list:
                formatted_lines.append('</ul>')
                in_list = False
            if in_ordered_list:
                formatted_lines.append('</ol>')
                in_ordered_list = False
            formatted_lines.append(f'<h{level}>{content}</h{level}>')
            continue

        # Listes numérotées -> <ol><li>...</li></ol>
        numbered_match = re.match(r'^\d+\.\s*(.*)', stripped)
        if numbered_match:
            content = numbered_match.group(1).strip()
            if in_list:
                formatted_lines.append('</ul>')
                in_list = False
            if not in_ordered_list:
                formatted_lines.append('<ol>')
                in_ordered_list = True
            formatted_lines.append(f'<li>{content}</li>')
            continue

        # Listes à puces -> <ul><li>...</li></ul>
        if stripped.startswith('- ') or stripped.startswith('* '):
            content = stripped[2:].strip()
            if in_ordered_list:
                formatted_lines.append('</ol>')
                in_ordered_list = False
            if not in_list:
                formatted_lines.append('<ul>')
                in_list = True
            formatted_lines.append(f'<li>{content}</li>')
            continue

        # Gestion des indentations (sous-listes)
        indent_match = re.match(r'^(\s+)[-*]\s*(.*)', stripped)
        if indent_match:
            content = indent_match.group(2).strip()
            if not in_list:
                formatted_lines.append('<ul>')
                in_list = True
            formatted_lines.append(f'<li style="margin-left: 20px;">{content}</li>')
            continue

        # Si c'est une ligne vide
        if not stripped:
            if in_list:
                formatted_lines.append('</ul>')
                in_list = False
            if in_ordered_list:
                formatted_lines.append('</ol>')
                in_ordered_list = False
            formatted_lines.append('<br>')
            continue

        # Sinon, texte brut (paragraphe)
        if in_list:
            formatted_lines.append('</ul>')
            in_list = False
        if in_ordered_list:
            formatted_lines.append('</ol>')
            in_ordered_list = False

        # Préserver les formatages en gras et italique
        stripped = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', stripped)
        stripped = re.sub(r'\*(.*?)\*', r'<em>\1</em>', stripped)
        stripped = re.sub(r'`(.*?)`', r'<code>\1</code>', stripped)

        formatted_lines.append(f'<p>{stripped}</p>')

    # Fermer les listes si elles sont encore ouvertes
    if in_list:
        formatted_lines.append('</ul>')
    if in_ordered_list:
        formatted_lines.append('</ol>')

    return ''.join(formatted_lines)

with open(input_file, newline='', encoding='utf-8-sig') as csv_in, \
     open(output_file, 'w', newline='', encoding='utf-8') as csv_out:

    reader = csv.DictReader(csv_in, delimiter=';')
    fieldnames = reader.fieldnames

    writer = csv.DictWriter(csv_out, fieldnames=fieldnames, delimiter=';', quoting=csv.QUOTE_ALL)
    writer.writeheader()

    for row in reader:
        for field in ['Description', 'Step Action']:
            row[field] = clean_and_format_text(row.get(field, ''))
        writer.writerow(row)

print(f"✅ Fichier corrigé généré avec HTML structuré pour Xray : {output_file}")
