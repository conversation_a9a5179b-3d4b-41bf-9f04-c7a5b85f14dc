import pandas as pd
import json

# Fichier source
input_file = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
output_file = "JIRA_for_Orange_processed.csv"

# Charger le CSV
df = pd.read_csv(input_file, encoding='utf-8', dtype=str)

# Liste pour stocker les nouvelles lignes
new_rows = []

for _, row in df.iterrows():
    manual_steps_raw = row.get("Custom field (Manual Test Steps)", "[]")
    
    try:
        steps = json.loads(manual_steps_raw)
        if not isinstance(steps, list):
            steps = []
    except json.JSONDecodeError:
        steps = []

    if steps:
        for step in steps:
            fields = step.get("fields", {})
            new_row = row.copy()
            new_row["Action"] = fields.get("Action", "")
            new_row["Data"] = fields.get("Data", "")
            new_row["Expected Result"] = fields.get("Expected Result", "")
            new_rows.append(new_row)
    else:
        # Pas de step, on garde la ligne telle quelle avec colonnes vides
        new_row = row.copy()
        new_row["Action"] = ""
        new_row["Data"] = ""
        new_row["Expected Result"] = ""
        new_rows.append(new_row)

# Créer le DataFrame final
df_final = pd.DataFrame(new_rows)

# Sauvegarder le nouveau fichier
df_final.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"✅ Traitement terminé. Nouveau fichier : {output_file}")
