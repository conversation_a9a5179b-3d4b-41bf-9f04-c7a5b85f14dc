"Summary","Issue key","Issue id","Issue Type","Status","Priority","Description","Component/s","Custom field (Test Repository Path)","Reporter","Assignee","Created","Updated","Project key","Project name","Action","Data","Expected Result"
"Documentation availability and conformance","SDMTESTP-290","8725090","Test","To Do","Medium","Report of error in documentation","COMMON","/SDM COMMON TESTCASES/DOCUMENTATION","<EMAIL>","","06/Jun/25 2:16 PM","10/Jun/25 3:28 PM","SDMTESTP","SDM TESTPLAN","","",""
"BE Geo-Redundancy","SDMTESTP-289","8724318","Test","To Do","Lowest","'- Normal case:\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.\n- synchronisation outage :\n	- Disable the synchronisation between 2 SDM , it could be done by either or following command:\n		- ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK\n		- SET NFCFG:NFID=207,NFState=""BLOCK""\n		- SYNA\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\n	- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207\n	- enable the  synchronization between 2 SDM, by either or following command:\n		- SET NFCFG:NFID=107,NFState=""NORMAL""\n		- SET NFCFG:NFID=207,NFState=""NORMAL""\n		- SYNA\n	- check data in webagent in SDM207, the changed data could be seen.","COMMON","","<EMAIL>","","06/Jun/25 11:19 AM","09/Jun/25 12:11 PM","SDMTESTP","SDM TESTPLAN","- normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.","","Same data in SDM107 and SDM207"
"BE Geo-Redundancy","SDMTESTP-289","8724318","Test","To Do","Lowest","'- Normal case:\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.\n- synchronisation outage :\n	- Disable the synchronisation between 2 SDM , it could be done by either or following command:\n		- ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK\n		- SET NFCFG:NFID=207,NFState=""BLOCK""\n		- SYNA\n	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107\n	- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207\n	- enable the  synchronization between 2 SDM, by either or following command:\n		- SET NFCFG:NFID=107,NFState=""NORMAL""\n		- SET NFCFG:NFID=207,NFState=""NORMAL""\n		- SYNA\n	- check data in webagent in SDM207, the changed data could be seen.","COMMON","","<EMAIL>","","06/Jun/25 11:19 AM","09/Jun/25 12:11 PM","SDMTESTP","SDM TESTPLAN","synchronization outage :
- 
	- disable the synchrnozation between 2 SDM , it could be done by either or following command:
		- SET NFCFG:NFID=107,NFState=""BLOCK""
		- SET NFCFG:NFID=207,NFState=""BLOCK""
	- change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107
	- login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
		- SET NFCFG:NFID=107,NFState=""NORMAL""
		- SET NFCFG:NFID=207,NFState=""NORMAL""
	- enable the  synchrnozation between 2 SDM, by either or following command:
	- check data in webagent in SDM207, the changed data could be seen.","","When synchronization is disabled :

The changed data in SDM107 is not seen in SDM207

When synchronization is enabled between the two SDMs:

The changed data in SDM107 is seen in SDM207"
"EPC HSS VoLTE SRVCC","SDMTESTP-286","8700061","Test","To Do","Low","SRVCC and its variants are network based mechanisms by which voice call continuity can be assured from IMS voice over PS access and CS voice service when the UE is capable of transmitting/receiving on only one of those access networks at a given time. they are intermediate solutions to meet the scenarios that VoLTE services can't assure voice continuity due to insufficient LTE coverage at the beginning of LTE rollout and wide legacy CS networks' coverage can be resorted to. Since LTE and VoLTE services are a fundamental part of next-generation mobile networks, SRVCC is a key capability while LTE coverage continues to be spotty.
 

SRVCC is defined in 3GPP TS 23.216 for voice call continuity from LTEtoCS (CircuitSwitched), SRVCC allows IMS session continuity (specified in 3GPP TS 23.237) when the UE has a single radio, thus only one RAT can be active at one time.
When moving out from IMS Voice capable LTE coverage, SRVCC allows voice continuity via handover to 2G/3G CS. It is considered an important business advantage for operators since it allows a superior VoIP service that cannot be matched by third party voice application providers until LTE coverage is perfected.

As an SRVCC-capable mobile engaged in a voice call determines that it is moving away from LTE coverage, it notifies the LTE network. The LTE network determines that the voice call needs to be moved to the legacy circuit domain. It notifies the MSC server of the need to switch the voice call from the packet to the circuit domain and initiates a handover of the LTE voice bearer to the circuit network. In this step, MME shall carry a STN-SR number identifying SCCAS in IMS core and C-MSISDN (correlation MSISDN to find the original call leg in IMS), which is downloaded from EPC HSS as the user initiates attach procedure. The MSC server establishes a bearer path for the mobile in the legacy network and notifies the IMS core that the mobile's call leg is moving from the packet to the circuit domain. The circuit-packet function in the IMS core then performs the necessary interworking functions. When the mobile arrive on-channel in the legacy network, it switches its internal voice processing from VoIP to legacy-circuit voice, and the call continues.","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","03/Jun/25 9:17 AM","06/Jun/25 5:27 PM","SDMTESTP","SDM TESTPLAN","- User A is provisionned with STN-SR value in his profile
- User A initiate a S6a MME registration
- MME support the SRVCC capability
- MME send a ULR request to the EPC-HSS","","- The EPC-HSS reply with ULA response that contain the STN-SR provisionned"
"HLR Any Time Interrogation - CS","SDMTESTP-276","8697580","Test","To Do","Low","With this procedure a network node can acquire the subscriber current location information and state information from the HLR","HLR","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management","<EMAIL>","","02/Jun/25 4:19 PM","06/Jun/25 5:41 PM","SDMTESTP","SDM TESTPLAN","|- Subscriber A is registered under VLR
- A network node (gsmSCF) send a ATI request for CS information retreival from the HLR|","","- On receipt of ATI request, the HLR initiate an PSI request to the VLR
- On receipt of PSI response the HLR return the ATI response with CS information requested"
" IMS HSS T-ADS SCC-AS retreive CSRN via Sh","SDMTESTP-284","8697577","Test","To Do","Low","","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","02/Jun/25 4:16 PM","06/Jun/25 5:37 PM","SDMTESTP","SDM TESTPLAN","","",""
" IMS HSS T-ADS SCC-AS retreive TADS via Sh","SDMTESTP-275","8697576","Test","To Do","Low","","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","02/Jun/25 4:16 PM","06/Jun/25 5:41 PM","SDMTESTP","SDM TESTPLAN","","",""
"IMS HSS T-ADS IMS Domain Prefered by HSS","SDMTESTP-279","8697399","Test","To Do","Low","HSS policy is defined to operate the call over IMS domain","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","02/Jun/25 4:10 PM","06/Jun/25 5:40 PM","SDMTESTP","SDM TESTPLAN","|- user A is attached in CS domain
- user A is attached in IMS domain
- HSS policy domain selection policy is defined as IMS prefered

- GMSC a SRI for user A|","","- HSS reply with IMRN number"
"IMS HSS T-ADS CS Domain Prefered by HSS","SDMTESTP-280","8697398","Test","To Do","Low","HSS policy is defined to operate the call over CS domain","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","02/Jun/25 4:10 PM","06/Jun/25 5:40 PM","SDMTESTP","SDM TESTPLAN","|- user A is attached in CS domain
- user A is attached in IMS domain
- HSS policy domain selection policy is defined as CS prefered

- GMSC a SRI for user A|","","- HSS reply with MRSN number"
"EPC HSS VoLTE eSRVCC","SDMTESTP-282","8697395","Test","To Do","Low","To minimize the voice interruption during the handover to CS if the Home IMS is located far away from the serving LTE network, A Rel-10 IMS HPLMN/VPLMN model (eSRVCC) introduces ATCF in VPLMN as the anchor of IMS session, instead of SCC AS like before. Since the new anchor is in VPLMN, its STN-SR is dynamic. To ensure that the MSC Server selects the correct ATCF during SRVCC procedure, the dynamic STN-SR pointing to the ATCF shall be provided to the MME before SRVCC procedure is triggered.

The ATCF shall allocate the its dynamic STN-SR when the user performs initial registration in the IMS. The STN-SR shall be provided through IMS and via third-party registration to the SCC AS. The SCC AS shall further provide the STN-SR to the HSS if the received STN-SR is different from the existing one that has been set, which in turn shall update the MME/SGSN. If requested the MME/SGSN shall indicate to the HSS if the UE has SRVCC capability. The SCC AS may inform the ATCF about the SRVCC capability of the UE.","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS VoLTE support","<EMAIL>","","02/Jun/25 4:09 PM","06/Jun/25 5:38 PM","SDMTESTP","SDM TESTPLAN","- User A is provisionned with STN-SR value in his profile
- User A initiate a S6a MME registration
- MME support the SRVCC capability
- MME send a ULR request to the EPC-HSS","","- The EPC-HSS reply with ULA response that contain the STN-SR provisionned"
"SWx_Reset","SDMTESTP-277","8697369","Test","To Do","Low","Test EPC non-3GPP access AAA Reset procedure","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx","<EMAIL>","","02/Jun/25 3:57 PM","06/Jun/25 5:41 PM","SDMTESTP","SDM TESTPLAN","|- From OAM provision a user Reset procedure
- The HSS send a PPR request with PPR-Flag that contain Reset-Indicator set|","","- The flag in PPR request with PPR-Flag contain Reset-Indicator set"
"SWx_SAR","SDMTESTP-283","8696973","Test","To Do","Low","Test EPC non-3GPP access Server Assignment Request","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx","<EMAIL>","","02/Jun/25 3:50 PM","06/Jun/25 5:38 PM","SDMTESTP","SDM TESTPLAN","","",""
"SWx_PPR","SDMTESTP-281","8696953","Test","To Do","Low","Test EPC non-3GPP access Push Profile Request","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx","<EMAIL>","","02/Jun/25 3:50 PM","06/Jun/25 5:39 PM","SDMTESTP","SDM TESTPLAN","","",""
" SWx_MAR","SDMTESTP-274","8696919","Test","To Do","Low","Test EPC non-3GPP access Multimedia Authentication Request","IMS-HSS","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HSS 4G-IMS Non Regression/IMS HSS Service SWx","<EMAIL>","","02/Jun/25 3:40 PM","06/Jun/25 5:42 PM","SDMTESTP","SDM TESTPLAN","- UE  start authentication procedure.
- The 3GPP AAA send a MAR message (EAP-AKA algorithm)","","- The HSS reply with MAA that contain :
   - 3GPP-SIP Authenticate
   - 3GPP-SIP Authorization
   - Confidentiality-Key
   - Integrity-Key"
"HLR Any Time Interrogation - PS","SDMTESTP-285","8696597","Test","To Do","Low","With this procedure a network node can acquire the subscriber current location information and state information from the HSS","HLR","/SDM COMMON TESTCASES/NON REGRESSION Testcases/HLR 2G-3G Non Regression/Location Management","<EMAIL>","","02/Jun/25 3:32 PM","06/Jun/25 5:30 PM","SDMTESTP","SDM TESTPLAN","|- Subscriber A is registered under MME
- A network node (gsmSCF) send a ATI request for PS information retreival from the HSS|","","- On receipt of ATI request, the HSS initiate an IDR request to the MME
- On receipt of IDA response the HSS return the ATI response with PS information requested"
"BE Geo-Redundancy","SDMTESTP-271","8674776","Test","To Do","Medium","# Normal case:
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there. 
 # synchronisation outage : 
 ## Disable the synchronisation between 2 SDM , it could be done by either or following command:
 ### ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK
 ### SET NFCFG:NFID=207,NFState=""BLOCK""
 ### SYNA
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107
 ## login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
 ## enable the  synchronization between 2 SDM, by either or following command:
 ###  SET NFCFG:NFID=107,NFState=""NORMAL""
 ### SET NFCFG:NFID=207,NFState=""NORMAL""
 ### SYNA
 ## check data in webagent in SDM207, the changed data could be seen.","COMMON","/SDM COMMON TESTCASES/RESILIENCY Testcases/Geographical Redundancy","<EMAIL>","","27/May/25 4:49 PM","20/Jun/25 10:15 PM","SDMTESTP","SDM TESTPLAN","# normal case ,   change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there.","","Same data in SDM107 and SDM207"
"BE Geo-Redundancy","SDMTESTP-271","8674776","Test","To Do","Medium","# Normal case:
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107,  apply the change then login sdm207 webagent,  to verify if changed data is there. 
 # synchronisation outage : 
 ## Disable the synchronisation between 2 SDM , it could be done by either or following command:
 ### ZTE:>SET NFCFG:NFID=""107"",NFNAME=""VDR-FUNC-SDM-107"",NFTYPE=self_nf,COMMTYPE=SCTP,NFSTATE=BLOCK
 ### SET NFCFG:NFID=207,NFState=""BLOCK""
 ### SYNA
 ## change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107
 ## login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
 ## enable the  synchronization between 2 SDM, by either or following command:
 ###  SET NFCFG:NFID=107,NFState=""NORMAL""
 ### SET NFCFG:NFID=207,NFState=""NORMAL""
 ### SYNA
 ## check data in webagent in SDM207, the changed data could be seen.","COMMON","/SDM COMMON TESTCASES/RESILIENCY Testcases/Geographical Redundancy","<EMAIL>","","27/May/25 4:49 PM","20/Jun/25 10:15 PM","SDMTESTP","SDM TESTPLAN","synchronization outage :    
 # 
 ## disable the synchrnozation between 2 SDM , it could be done by either or following command:
 ### SET NFCFG:NFID=107,NFState=""BLOCK""
 ### SET NFCFG:NFID=207,NFState=""BLOCK""
 ##  change HLR/EPC HSS profile in SDM107，  for example,  login webagent in SDM107
 ## login web agent in SDM207, the changed data in SDM107 is not seen in SDM207
 ### SET NFCFG:NFID=107,NFState=""NORMAL""
 ### SET NFCFG:NFID=207,NFState=""NORMAL""
 ## enable the  synchrnozation between 2 SDM, by either or following command:
 ## check data in webagent in SDM207, the changed data could be seen.","","When synchronization is disabled :

The changed data in SDM107 is not seen in SDM207

When synchronization is enabled between the two SDMs:

The changed data in SDM107 is seen in SDM207"
"FE Geo Redundancy","SDMTESTP-270","8674752","Test","To Do","Medium","FE from SDM107 and SDM 207 are naturely working in load-sharing mode without need any configuration.  which means that, no matter message is sent to sdm107 or sdm207,   it could and should be processed successfully.   

BE from SDM107 and SDM 207 more complicated:  in BE level, both BE is working on load-sharing mode  but with bit of difference with the FE:
 * for reading message such as SRI, SRIforSMS,    all BE shares this type of traffic.   FE could access its own BE and complete service processing.
 * for modifying messages such as updatelocation,    DSA clusters in BE working in active-standby mode.   For the testbed case, there are 2 dsa clusters and  primary dsa node in all dsa clusters are  in SDM107,  then  for write message,  in normal case, no matter it is sent to FE in SDM107 or FE in SDM 207, then eventually, it is processed in primary DSA node in SDM107.    
 * message to SDM107,  FE direct applies change on primary node 1402 in SDM107, then changed data is sychronized to node 2402 in SDM207. 
 * messsage to SDM207, FE in SDM207 also applies change on node 1402 in SDM107, then changed data is sychronized to node 2402 in SDM207. 


Architecture of testbed :

!image-2025-05-27-16-59-57-197.png!","COMMON","/SDM COMMON TESTCASES/RESILIENCY Testcases/Geographical Redundancy","<EMAIL>","","27/May/25 4:40 PM","03/Jun/25 6:36 PM","SDMTESTP","SDM TESTPLAN","FE Geo-Redundancy test. 
 # sends all traffic to SDM107 and it is supposed to be processed successfully. 
 # same traffic to SDM207,  and  it is supposed to be processed successfully.","","# traffic to SDM107 is processed successfully. 
 # traffic to SDM207 is processed successfully."
