#!/usr/bin/env python3
"""
Démonstration complète de l'extracteur avec tous les rapports
"""

import os
import glob
from datetime import datetime

def show_complete_demo():
    """Affiche une démonstration complète de tous les fichiers générés"""
    
    print("🎯 DÉMONSTRATION COMPLÈTE - EXTRACTEUR ÉTAPES DE TEST")
    print("=" * 65)
    
    print(f"\n📅 Exécution du: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
    
    # Fichiers d'entrée
    print(f"\n📥 FICHIER D'ENTRÉE:")
    print("-" * 25)
    input_file = "JIRA for Orange 2025-07-01T12_00_00+0200.csv"
    if os.path.exists(input_file):
        size = os.path.getsize(input_file) / 1024
        print(f"   ✅ {input_file} ({size:.1f} KB)")
    else:
        print(f"   ❌ {input_file} (non trouvé)")
    
    # Fichiers générés
    print(f"\n📤 FICHIERS GÉNÉRÉS:")
    print("-" * 25)
    
    generated_files = [
        ("JIRA for Orange 2025-07-01T12_00_00+0200_steps_extracted.csv", "Fichier principal pour import"),
        ("summary_extracted_steps_v2.csv", "Aperçu simplifié"),
        ("README.md", "Documentation complète")
    ]
    
    for filename, description in generated_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename) / 1024
            print(f"   ✅ {filename}")
            print(f"      → {description} ({size:.1f} KB)")
        else:
            print(f"   ❌ {filename} (non trouvé)")
    
    # Rapports d'exécution
    print(f"\n📄 RAPPORTS D'EXÉCUTION:")
    print("-" * 30)
    
    report_files = glob.glob("execution_report_*.txt")
    if report_files:
        # Trier par date (plus récent en premier)
        report_files.sort(reverse=True)
        for i, report in enumerate(report_files[:3]):  # Afficher les 3 plus récents
            size = os.path.getsize(report) / 1024
            timestamp = report.replace("execution_report_", "").replace(".txt", "")
            formatted_time = f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]} {timestamp[9:11]}:{timestamp[11:13]}:{timestamp[13:15]}"
            status = "🆕" if i == 0 else "📋"
            print(f"   {status} {report}")
            print(f"      → Généré le {formatted_time} ({size:.1f} KB)")
    else:
        print("   ❌ Aucun rapport trouvé")
    
    # Scripts disponibles
    print(f"\n🛠️ SCRIPTS DISPONIBLES:")
    print("-" * 25)
    
    scripts = [
        ("extract_test_steps.py", "Script principal d'extraction"),
        ("validate_extraction.py", "Validation et statistiques"),
        ("create_summary_view.py", "Création d'aperçu simplifié"),
        ("analyze_columns.py", "Analyse des colonnes source"),
        ("final_report_v2.py", "Rapport final détaillé"),
        ("demo_complete.py", "Cette démonstration")
    ]
    
    for script, description in scripts:
        if os.path.exists(script):
            print(f"   ✅ {script}")
            print(f"      → {description}")
        else:
            print(f"   ❌ {script} (non trouvé)")
    
    # Statistiques globales
    show_global_stats()
    
    print(f"\n🎉 SYSTÈME COMPLET ET OPÉRATIONNEL!")
    print(f"📋 Consultez README.md pour la documentation complète")

def show_global_stats():
    """Affiche les statistiques globales du projet"""
    
    print(f"\n📊 STATISTIQUES GLOBALES:")
    print("-" * 30)
    
    # Compter les fichiers
    csv_files = len(glob.glob("*.csv"))
    py_files = len(glob.glob("*.py"))
    txt_files = len(glob.glob("*.txt"))
    md_files = len(glob.glob("*.md"))
    
    print(f"   📁 Fichiers CSV: {csv_files}")
    print(f"   🐍 Scripts Python: {py_files}")
    print(f"   📄 Rapports TXT: {txt_files}")
    print(f"   📋 Documentation MD: {md_files}")
    
    total_files = csv_files + py_files + txt_files + md_files
    print(f"   📦 Total fichiers: {total_files}")
    
    # Taille totale
    total_size = 0
    for pattern in ["*.csv", "*.py", "*.txt", "*.md"]:
        for file in glob.glob(pattern):
            total_size += os.path.getsize(file)
    
    print(f"   💾 Taille totale: {total_size / 1024:.1f} KB")

def show_next_steps():
    """Affiche les prochaines étapes recommandées"""
    
    print(f"\n🚀 PROCHAINES ÉTAPES RECOMMANDÉES:")
    print("-" * 40)
    print("1. 📋 Consultez le README.md pour la documentation")
    print("2. 🔍 Vérifiez le fichier principal généré")
    print("3. 📊 Consultez le dernier rapport d'exécution")
    print("4. 🎯 Importez dans votre projet Jira/Xray")
    print("5. ✅ Mappez les colonnes lors de l'import")

if __name__ == '__main__':
    show_complete_demo()
    show_next_steps()
